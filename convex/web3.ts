import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Track token usage
export const trackTokenUsage = mutation({
  args: {
    userId: v.id("users"),
    tokens: v.number(),
    model: v.string(),
  },
  handler: async (ctx, args) => {
    const month = new Date().toISOString().slice(0, 7); // YYYY-MM
    
    await ctx.db.insert("tokenUsage", {
      userId: args.userId,
      tokens: args.tokens,
      model: args.model,
      timestamp: new Date().toISOString(),
      month,
    });

    return { success: true };
  },
});

// Get token usage for user
export const getTokenUsage = query({
  args: {
    userId: v.id("users"),
    month: v.optional(v.string()), // YYYY-MM format
  },
  handler: async (ctx, args) => {
    const targetMonth = args.month || new Date().toISOString().slice(0, 7);
    
    const usage = await ctx.db
      .query("tokenUsage")
      .withIndex("by_month", (q) => q.eq("month", targetMonth))
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .collect();

    const totalTokens = usage.reduce((sum, u) => sum + u.tokens, 0);
    const byModel = usage.reduce((acc, u) => {
      acc[u.model] = (acc[u.model] || 0) + u.tokens;
      return acc;
    }, {} as Record<string, number>);

    return {
      month: targetMonth,
      totalTokens,
      byModel,
      details: usage,
    };
  },
});

// Check rate limit
export const checkRateLimit = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) return null;

    const windowStart = new Date();
    windowStart.setHours(windowStart.getHours() - 1); // 1-hour window
    const windowStartStr = windowStart.toISOString();

    const rateLimit = await ctx.db
      .query("rateLimits")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.gte(q.field("windowStart"), windowStartStr))
      .first();

    const limits = {
      FREE: 10,
      BRONZE: 50,
      SILVER: 200,
      DIAMOND: 1000,
    };

    const limit = limits[user.tier];
    const used = rateLimit?.requests || 0;
    const remaining = Math.max(0, limit - used);

    return {
      tier: user.tier,
      limit,
      used,
      remaining,
      allowed: remaining > 0,
    };
  },
});

// Increment rate limit
export const incrementRateLimit = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const windowStart = new Date();
    windowStart.setHours(windowStart.getHours() - 1);
    const windowStartStr = windowStart.toISOString();

    const existing = await ctx.db
      .query("rateLimits")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.gte(q.field("windowStart"), windowStartStr))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        requests: existing.requests + 1,
      });
    } else {
      await ctx.db.insert("rateLimits", {
        userId: args.userId,
        requests: 1,
        windowStart: new Date().toISOString(),
        tier: user.tier,
      });
    }

    return { success: true };
  },
});

// Create or update staking position
export const upsertStakingPosition = mutation({
  args: {
    userId: v.id("users"),
    amount: v.string(),
    tier: v.number(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("stakingPositions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    const now = new Date().toISOString();

    if (existing) {
      await ctx.db.patch(existing._id, {
        amount: args.amount,
        tier: args.tier,
        lastClaimTime: now,
      });
      return existing._id;
    } else {
      return await ctx.db.insert("stakingPositions", {
        userId: args.userId,
        amount: args.amount,
        tier: args.tier,
        startTime: now,
        lastClaimTime: now,
        auraPoints: 0,
        isActive: true,
      });
    }
  },
});

// Claim aura points
export const claimAuraPoints = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const position = await ctx.db
      .query("stakingPositions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!position) {
      throw new Error("No active staking position");
    }

    // Calculate points based on time elapsed and tier
    const lastClaim = new Date(position.lastClaimTime);
    const now = new Date();
    const hoursElapsed = (now.getTime() - lastClaim.getTime()) / (1000 * 60 * 60);
    
    const pointsPerHour = {
      0: 1,  // Bronze/Common tier
      1: 2,  // Silver/Rare tier
      2: 5,  // Diamond/Legendary tier
    };

    const earnedPoints = Math.floor(hoursElapsed * (pointsPerHour[position.tier] || 1));

    if (earnedPoints > 0) {
      await ctx.db.patch(position._id, {
        auraPoints: position.auraPoints + earnedPoints,
        lastClaimTime: now.toISOString(),
      });
    }

    return {
      earnedPoints,
      totalPoints: position.auraPoints + earnedPoints,
    };
  },
});

// Get staking position
export const getStakingPosition = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("stakingPositions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();
  },
});