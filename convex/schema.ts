import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    clerkId: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    walletAddress: v.optional(v.string()),
    walletLinkedAt: v.optional(v.string()),
    tier: v.union(
      v.literal("FREE"),
      v.literal("BRONZE"),
      v.literal("SILVER"),
      v.literal("DIAMOND")
    ),
    tokenBalance: v.number(),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_clerk_id", ["clerkId"])
    .index("by_wallet", ["walletAddress"])
    .index("by_email", ["email"]),

  chats: defineTable({
    userId: v.id("users"),
    title: v.string(),
    visibility: v.union(v.literal("public"), v.literal("private")),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_user", ["userId"])
    .index("by_visibility", ["visibility"])
    .index("by_created", ["createdAt"]),

  messages: defineTable({
    chatId: v.id("chats"),
    role: v.union(v.literal("user"), v.literal("assistant"), v.literal("system")),
    content: v.string(),
    parts: v.array(
      v.object({
        type: v.string(),
        content: v.string(),
      })
    ),
    attachments: v.array(
      v.object({
        url: v.string(),
        name: v.string(),
        type: v.string(),
        size: v.number(),
      })
    ),
    model: v.optional(v.string()),
    tokenCount: v.optional(v.number()),
    createdAt: v.string(),
  })
    .index("by_chat", ["chatId"])
    .index("by_created", ["createdAt"]),

  documents: defineTable({
    userId: v.id("users"),
    chatId: v.optional(v.id("chats")),
    title: v.string(),
    content: v.string(),
    kind: v.union(
      v.literal("text"),
      v.literal("code"),
      v.literal("image"),
      v.literal("sheet")
    ),
    language: v.optional(v.string()),
    metadata: v.optional(v.any()),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_user", ["userId"])
    .index("by_chat", ["chatId"])
    .index("by_kind", ["kind"]),

  votes: defineTable({
    userId: v.id("users"),
    messageId: v.id("messages"),
    isUpvoted: v.boolean(),
    createdAt: v.string(),
  })
    .index("by_message", ["messageId"])
    .index("by_user", ["userId"]),

  suggestions: defineTable({
    documentId: v.id("documents"),
    userId: v.id("users"),
    originalText: v.string(),
    suggestedText: v.string(),
    description: v.optional(v.string()),
    isResolved: v.boolean(),
    createdAt: v.string(),
  })
    .index("by_document", ["documentId"])
    .index("by_user", ["userId"])
    .index("by_resolved", ["isResolved"]),

  // Web3-specific tables
  stakingPositions: defineTable({
    userId: v.id("users"),
    amount: v.string(), // Store as string for precision
    tier: v.number(),
    startTime: v.string(),
    lastClaimTime: v.string(),
    auraPoints: v.number(),
    isActive: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_active", ["isActive"]),

  tokenUsage: defineTable({
    userId: v.id("users"),
    tokens: v.number(),
    model: v.string(),
    timestamp: v.string(),
    month: v.string(), // YYYY-MM format for monthly aggregation
  })
    .index("by_user", ["userId"])
    .index("by_month", ["month"])
    .index("by_timestamp", ["timestamp"]),

  rateLimits: defineTable({
    userId: v.id("users"),
    requests: v.number(),
    windowStart: v.string(),
    tier: v.string(),
  })
    .index("by_user", ["userId"])
    .index("by_window", ["windowStart"]),

  streams: defineTable({
    streamId: v.string(),
    chatId: v.id("chats"),
    userId: v.id("users"),
    isActive: v.boolean(),
    createdAt: v.string(),
  })
    .index("by_stream_id", ["streamId"])
    .index("by_chat", ["chatId"])
    .index("by_active", ["isActive"]),

  // Telegram-specific tables
  telegramUsers: defineTable({
    userId: v.id("users"),
    telegramId: v.string(),
    username: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    languageCode: v.optional(v.string()),
    isPremium: v.boolean(),
    createdAt: v.string(),
  })
    .index("by_telegram_id", ["telegramId"])
    .index("by_user", ["userId"]),

  bankaiMoves: defineTable({
    userId: v.id("users"),
    moveType: v.string(),
    damage: v.number(),
    cost: v.string(),
    timestamp: v.string(),
    txHash: v.optional(v.string()),
  })
    .index("by_user", ["userId"])
    .index("by_timestamp", ["timestamp"]),
});