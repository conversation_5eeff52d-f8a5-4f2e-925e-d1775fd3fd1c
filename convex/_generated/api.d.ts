/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type * as chats from "../chats.js";
import type * as documents from "../documents.js";
import type * as messages from "../messages.js";
import type * as users from "../users.js";
import type * as web3 from "../web3.js";

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  chats: typeof chats;
  documents: typeof documents;
  messages: typeof messages;
  users: typeof users;
  web3: typeof web3;
}>;
export type Mounts = {
  chats: {
    createChat: FunctionReference<"mutation">;
    deleteChat: FunctionReference<"mutation">;
    getChatById: FunctionReference<"query">;
    getPublicChats: FunctionReference<"query">;
    getUserChats: FunctionReference<"query">;
    updateChatTitle: FunctionReference<"mutation">;
  };
  documents: {
    createDocument: FunctionReference<"mutation">;
    createSuggestion: FunctionReference<"mutation">;
    deleteDocument: FunctionReference<"mutation">;
    getDocumentById: FunctionReference<"query">;
    getUserDocuments: FunctionReference<"query">;
    resolveSuggestion: FunctionReference<"mutation">;
    updateDocument: FunctionReference<"mutation">;
  };
  messages: {
    createMessage: FunctionReference<"mutation">;
    getChatMessages: FunctionReference<"query">;
    getMessageById: FunctionReference<"query">;
    removeVote: FunctionReference<"mutation">;
    streamMessage: FunctionReference<"mutation">;
    voteMessage: FunctionReference<"mutation">;
  };
  users: {
    getUserByClerkId: FunctionReference<"query">;
    getUserStats: FunctionReference<"query">;
    linkWallet: FunctionReference<"mutation">;
    unlinkWallet: FunctionReference<"mutation">;
    updateUserTier: FunctionReference<"mutation">;
    upsertUser: FunctionReference<"mutation">;
  };
  web3: {
    checkRateLimit: FunctionReference<"query">;
    claimAuraPoints: FunctionReference<"mutation">;
    getStakingPosition: FunctionReference<"query">;
    getTokenUsage: FunctionReference<"query">;
    incrementRateLimit: FunctionReference<"mutation">;
    trackTokenUsage: FunctionReference<"mutation">;
    upsertStakingPosition: FunctionReference<"mutation">;
  };
};

export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;