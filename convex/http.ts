import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";

const http = httpRouter();

// Health check endpoint
http.route({
  path: "/health",
  method: "GET",
  handler: httpAction(async () => {
    return new Response("OK", {
      status: 200,
      headers: {
        "Content-Type": "text/plain",
      },
    });
  }),
});

// Webhook endpoint for Clerk
http.route({
  path: "/clerk-webhook",
  method: "POST",
  handler: httpAction(async ({ runMutation }, request) => {
    const payload = await request.json();
    
    // Handle Clerk webhook events
    if (payload.type === "user.created" || payload.type === "user.updated") {
      const { id, email_addresses, first_name, last_name, image_url, public_metadata } = payload.data;
      
      await runMutation("users:upsertUser", {
        clerkId: id,
        email: email_addresses[0]?.email_address || "",
        name: `${first_name || ""} ${last_name || ""}`.trim() || "Anonymous",
        imageUrl: image_url || undefined,
        walletAddress: public_metadata?.walletAddress as string | undefined,
        tier: "FREE",
      });
    }
    
    return new Response("OK", { status: 200 });
  }),
});

export default http;