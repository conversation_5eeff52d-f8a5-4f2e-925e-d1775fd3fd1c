import { useQuery, useMutation, useConvexAuth } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";

// User hooks
export function useCurrentUser() {
  const { isAuthenticated } = useConvexAuth();
  const user = useQuery(
    api.users.getUserByClerkId,
    isAuthenticated ? { clerkId: "current" } : "skip"
  );
  return user;
}

export function useUserStats(userId: Id<"users">) {
  return useQuery(api.users.getUserStats, { userId });
}

export function useLinkWallet() {
  return useMutation(api.users.linkWallet);
}

export function useUnlinkWallet() {
  return useMutation(api.users.unlinkWallet);
}

// Chat hooks
export function useCreateChat() {
  return useMutation(api.chats.createChat);
}

export function useChat(chatId: Id<"chats">) {
  return useQuery(api.chats.getChatById, { chatId });
}

export function useUserChats(userId: Id<"users">, limit = 50) {
  return useQuery(api.chats.getUserChats, {
    userId,
    paginationOpts: { numItems: limit, cursor: null },
  });
}

export function usePublicChats(limit = 50) {
  return useQuery(api.chats.getPublicChats, {
    paginationOpts: { numItems: limit, cursor: null },
  });
}

export function useUpdateChatTitle() {
  return useMutation(api.chats.updateChatTitle);
}

export function useDeleteChat() {
  return useMutation(api.chats.deleteChat);
}

// Message hooks
export function useChatMessages(chatId: Id<"chats">, limit = 50) {
  return useQuery(api.messages.getChatMessages, {
    chatId,
    paginationOpts: { numItems: limit, cursor: null },
  });
}

export function useCreateMessage() {
  return useMutation(api.messages.createMessage);
}

export function useStreamMessage() {
  return useMutation(api.messages.streamMessage);
}

export function useVoteMessage() {
  return useMutation(api.messages.voteMessage);
}

export function useRemoveVote() {
  return useMutation(api.messages.removeVote);
}

// Document hooks
export function useCreateDocument() {
  return useMutation(api.documents.createDocument);
}

export function useUpdateDocument() {
  return useMutation(api.documents.updateDocument);
}

export function useDocument(documentId: Id<"documents">) {
  return useQuery(api.documents.getDocumentById, { documentId });
}

export function useUserDocuments(userId: Id<"users">, kind?: string, limit = 50) {
  return useQuery(api.documents.getUserDocuments, {
    userId,
    kind: kind as any,
    paginationOpts: { numItems: limit, cursor: null },
  });
}

export function useDeleteDocument() {
  return useMutation(api.documents.deleteDocument);
}

export function useCreateSuggestion() {
  return useMutation(api.documents.createSuggestion);
}

export function useResolveSuggestion() {
  return useMutation(api.documents.resolveSuggestion);
}

// Web3 hooks
export function useTokenUsage(userId: Id<"users">, month?: string) {
  return useQuery(api.web3.getTokenUsage, { userId, month });
}

export function useRateLimit(userId: Id<"users">) {
  return useQuery(api.web3.checkRateLimit, { userId });
}

export function useTrackTokenUsage() {
  return useMutation(api.web3.trackTokenUsage);
}

export function useIncrementRateLimit() {
  return useMutation(api.web3.incrementRateLimit);
}

export function useStakingPosition(userId: Id<"users">) {
  return useQuery(api.web3.getStakingPosition, { userId });
}

export function useUpsertStakingPosition() {
  return useMutation(api.web3.upsertStakingPosition);
}

export function useClaimAuraPoints() {
  return useMutation(api.web3.claimAuraPoints);
}