import { Context } from "../../bundler/context.js";
import { ComponentDirectory } from "../lib/components/definition/directoryStructure.js";
import { StartPushResponse } from "../lib/deployApi/startPush.js";
export declare function noSchemaDataModelDTS(): string;
export declare function dynamicDataModelDTS(): string;
export declare function staticDataModelDTS(ctx: Context, startPush: StartPushResponse, rootComponent: ComponentDirectory, componentDirectory: ComponentDirectory): Promise<string>;
//# sourceMappingURL=dataModel.d.ts.map