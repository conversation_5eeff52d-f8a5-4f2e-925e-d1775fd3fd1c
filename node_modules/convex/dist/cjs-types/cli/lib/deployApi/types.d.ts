import { z } from "zod";
export declare const reference: z.ZodString;
export type Reference = z.infer<typeof reference>;
export declare const authInfo: z.ZodU<PERSON>n<[z.ZodObject<{
    type: z.ZodLiteral<"customJwt">;
    applicationID: z.ZodNullable<z.ZodString>;
    issuer: z.ZodString;
    jwks: z.ZodString;
    algorithm: z.ZodString;
}, "passthrough", z.<PERSON>, z.objectOutputType<{
    type: z.ZodLiteral<"customJwt">;
    applicationID: z.ZodNullable<z.ZodString>;
    issuer: z.ZodString;
    jwks: z.ZodString;
    algorithm: z.ZodString;
}, z.ZodType<PERSON>ny, "passthrough">, z.objectInputType<{
    type: z.ZodLiteral<"customJwt">;
    applicationID: z.ZodNullable<z.ZodString>;
    issuer: z.ZodString;
    jwks: z.Zod<PERSON>tring;
    algorithm: z.ZodString;
}, z.ZodTypeAny, "passthrough">>, z.ZodObject<{
    applicationID: z.ZodString;
    domain: z.ZodString;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    applicationID: z.ZodString;
    domain: z.ZodString;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    applicationID: z.ZodString;
    domain: z.ZodString;
}, z.ZodTypeAny, "passthrough">>]>;
export type AuthInfo = z.infer<typeof authInfo>;
export declare const identifier: z.ZodString;
export type Identifier = z.infer<typeof identifier>;
//# sourceMappingURL=types.d.ts.map