import { z } from "zod";
export declare const componentDefinitionPath: z.ZodString;
export type ComponentDefinitionPath = z.infer<typeof componentDefinitionPath>;
export declare const componentPath: z.ZodString;
export type ComponentPath = z.infer<typeof componentPath>;
export declare const canonicalizedModulePath: z.ZodString;
export type CanonicalizedModulePath = z.infer<typeof canonicalizedModulePath>;
export declare const componentFunctionPath: z.ZodObject<{
    component: z.ZodString;
    udfPath: z.ZodString;
}, "passthrough", z.ZodTypeAny, {
    udfPath: string;
    component: string;
}, {
    udfPath: string;
    component: string;
}>;
export type ComponentFunctionPath = z.infer<typeof componentFunctionPath>;
//# sourceMappingURL=paths.d.ts.map