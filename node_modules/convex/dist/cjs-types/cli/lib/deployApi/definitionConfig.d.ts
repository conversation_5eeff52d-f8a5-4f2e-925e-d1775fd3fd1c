import { z } from "zod";
export declare const appDefinitionConfig: z.ZodObject<{
    definition: z.ZodNullable<z.ZodObject<{
        path: z.ZodString;
        source: z.ZodString;
        sourceMap: z.Zod<PERSON>ptional<z.ZodString>;
        environment: z.<PERSON><[z.ZodLiteral<"isolate">, z.<PERSON><"node">]>;
    }, "passthrough", z.<PERSON>od<PERSON>ype<PERSON>, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }>>;
    dependencies: z.<PERSON>od<PERSON>rray<z.ZodString, "many">;
    schema: z.ZodNullable<z.ZodObject<{
        path: z.ZodString;
        source: z.ZodString;
        sourceMap: z.ZodOptional<z.ZodString>;
        environment: z.ZodU<PERSON>n<[z.ZodLiteral<"isolate">, z.<PERSON><"node">]>;
    }, "passthrough", z.<PERSON>, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }>>;
    functions: z.ZodArray<z.ZodObject<{
        path: z.ZodString;
        source: z.ZodString;
        sourceMap: z.ZodOptional<z.ZodString>;
        environment: z.ZodUnion<[z.ZodLiteral<"isolate">, z.ZodLiteral<"node">]>;
    }, "passthrough", z.ZodTypeAny, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }>, "many">;
    udfServerVersion: z.ZodString;
}, "passthrough", z.ZodTypeAny, {
    definition: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    } | null;
    dependencies: string[];
    schema: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    } | null;
    functions: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }[];
    udfServerVersion: string;
}, {
    definition: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    } | null;
    dependencies: string[];
    schema: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    } | null;
    functions: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }[];
    udfServerVersion: string;
}>;
export type AppDefinitionConfig = z.infer<typeof appDefinitionConfig>;
export declare const componentDefinitionConfig: z.ZodObject<{
    definitionPath: z.ZodString;
    definition: z.ZodObject<{
        path: z.ZodString;
        source: z.ZodString;
        sourceMap: z.ZodOptional<z.ZodString>;
        environment: z.ZodUnion<[z.ZodLiteral<"isolate">, z.ZodLiteral<"node">]>;
    }, "passthrough", z.ZodTypeAny, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }>;
    dependencies: z.ZodArray<z.ZodString, "many">;
    schema: z.ZodNullable<z.ZodObject<{
        path: z.ZodString;
        source: z.ZodString;
        sourceMap: z.ZodOptional<z.ZodString>;
        environment: z.ZodUnion<[z.ZodLiteral<"isolate">, z.ZodLiteral<"node">]>;
    }, "passthrough", z.ZodTypeAny, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }>>;
    functions: z.ZodArray<z.ZodObject<{
        path: z.ZodString;
        source: z.ZodString;
        sourceMap: z.ZodOptional<z.ZodString>;
        environment: z.ZodUnion<[z.ZodLiteral<"isolate">, z.ZodLiteral<"node">]>;
    }, "passthrough", z.ZodTypeAny, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }, {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }>, "many">;
    udfServerVersion: z.ZodString;
}, "passthrough", z.ZodTypeAny, {
    definition: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    };
    dependencies: string[];
    schema: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    } | null;
    functions: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }[];
    definitionPath: string;
    udfServerVersion: string;
}, {
    definition: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    };
    dependencies: string[];
    schema: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    } | null;
    functions: {
        path: string;
        environment: "node" | "isolate";
        source: string;
        sourceMap?: string | undefined;
    }[];
    definitionPath: string;
    udfServerVersion: string;
}>;
export type ComponentDefinitionConfig = z.infer<typeof componentDefinitionConfig>;
//# sourceMappingURL=definitionConfig.d.ts.map