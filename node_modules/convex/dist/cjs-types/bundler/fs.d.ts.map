{"version": 3, "file": "fs.d.ts", "sourceRoot": "", "sources": ["../../../src/bundler/fs.ts"], "names": [], "mappings": ";;AAKA,OAAO,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC;AAK5D,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAElC,MAAM,MAAM,cAAc,GAAG,MAAM,CAAC;AAgDpC,MAAM,WAAW,UAAU;IACzB,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IAEnC,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC;IAC9B,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC;IAC1B,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;IAKnC,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QAAE,aAAa,CAAC,EAAE,MAAM,CAAA;KAAE,GAClC,UAAU,CAAC;IACd,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC;IACjE,KAAK,CACH,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,OAAO,CAAC;QAAC,SAAS,CAAC,EAAE,OAAO,CAAA;KAAE,GACzD,IAAI,CAAC;IACR,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEtD,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;IACnD,UAAU,IAAI,IAAI,CAAC;CACpB;AAED,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG;IAAE,UAAU,EAAE,UAAU,CAAA;CAAE,CAAC;AAE3D,MAAM,WAAW,OAAO;IACtB,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,CAAC;IAC1C,eAAe,CACb,IAAI,EAAE,QAAQ,EACd,MAAM,EAAE,QAAQ,EAChB,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,GAC5B,OAAO,CAAC,IAAI,CAAC,CAAC;IACjB,gBAAgB,CAAC,EAAE,EAAE,KAAK,GAAG,IAAI,GAAG,QAAQ,CAAC;IAC7C,IAAI,EAAE,QAAQ,CAAC;CAChB;AAED,wBAAsB,UAAU,CAC9B,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,GAC3C,OAAO,CAAC,IAAI,CAAC,CA4Bf;AAKD,qBAAa,MAAO,YAAW,UAAU;IACvC,OAAO,CAAC,OAAO,EAAE,MAAM;IAGvB,MAAM,CAAC,IAAI,EAAE,MAAM;IAWnB,IAAI,CAAC,IAAI,EAAE,MAAM;IAGjB,YAAY,CAAC,IAAI,EAAE,MAAM;IAGzB,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QAAE,aAAa,CAAC,EAAE,MAAM,CAAA;KAAE,GAClC,UAAU;IAMP,eAAe,CACnB,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,QAAQ,EAChB,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,GAC5B,OAAO,CAAC,IAAI,CAAC;IAiBhB,MAAM,CAAC,IAAI,EAAE,MAAM;IAGnB,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI;IASzD,KAAK,CACH,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,OAAO,CAAC;QAAC,SAAS,CAAC,EAAE,OAAO,CAAA;KAAE,GACzD,IAAI;IAUP,KAAK,CAAC,IAAI,EAAE,MAAM;IAGlB,MAAM,CAAC,IAAI,EAAE,MAAM;IAGnB,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM;IAa9C,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,GAAG,IAAI;IAG7C,UAAU;CAGX;AACD,eAAO,MAAM,MAAM,QAAe,CAAC;AAKnC,qBAAa,WAAY,YAAW,UAAU;IAE5C,OAAO,CAAC,mBAAmB,CAAuC;IAGlE,OAAO,CAAC,aAAa,CAAwC;IAG7D,OAAO,CAAC,WAAW,CAAS;IAE5B,OAAO,CAAC,WAAW,CAAU;gBAEjB,WAAW,EAAE,OAAO;IAIhC,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,EAAE;IAoClC,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAa7B,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,KAAK;IAYzB,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAYlC,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QAAE,aAAa,CAAC,EAAE,MAAM,CAAA;KAAE,GAClC,UAAU;IAYb,MAAM,CAAC,IAAI,EAAE,MAAM;IAanB,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI;IAQ7D,KAAK,CACH,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,OAAO,CAAC;QAAC,SAAS,CAAC,EAAE,OAAO,CAAA;KAAE,GACzD,IAAI;IAeP,KAAK,CAAC,OAAO,EAAE,MAAM;IAKrB,MAAM,CAAC,QAAQ,EAAE,MAAM;IAKvB,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM;IAM9C,OAAO,CAAC,aAAa;IAerB,OAAO,CAAC,cAAc;IAYtB,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,GAAG,IAAI;IAKxC,UAAU;IAIV,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,GAAG,IAAI,GAAG,IAAI;IAqBjE,QAAQ,IAAI,YAAY,GAAG,aAAa;CAMzC;AAED,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,KAAK,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC;IAC3D,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,qBAAa,YAAY;IACvB,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACtC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;gBAG/B,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,EACrC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC;IAMlC,KAAK,IAAI,MAAM,EAAE;IAWjB,QAAQ,CAAC,EACP,OAAO,GACR,EAAE,UAAU,GAAG;QAAE,QAAQ,EAAE,KAAK,CAAA;KAAE,GAAG;QAAE,QAAQ,EAAE,IAAI,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;CA2CzE;AAcD,wBAAgB,SAAS,CACvB,CAAC,EAAE,KAAK,GAAG,IAAI,EACf,CAAC,EAAE,KAAK,GAAG,IAAI,GACd;IAAE,OAAO,EAAE,IAAI,CAAA;CAAE,GAAG;IAAE,OAAO,EAAE,KAAK,CAAC;IAAC,MAAM,EAAE,MAAM,CAAA;CAAE,CAsDxD;AAGD,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,UAOtD"}