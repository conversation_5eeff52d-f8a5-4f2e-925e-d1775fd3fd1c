{"version": 3, "file": "context.d.ts", "sourceRoot": "", "sources": ["../../../src/bundler/context.ts"], "names": [], "mappings": "AAEA,OAAY,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAU,MAAM,SAAS,CAAC;AAE7C,OAAO,WAAW,MAAM,UAAU,CAAC;AAGnC,MAAM,MAAM,SAAS,GAKjB,yBAAyB,GAKzB;IACE,+BAA+B,EAAE;QAC/B,SAAS,EAAE,MAAM,CAAC;QAClB,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB,GAAG,IAAI,CAAC;CACV,GAKD,gCAAgC,GAGhC,WAAW,GAGX,OAAO,CAAC;AAEZ,MAAM,MAAM,YAAY,GAAG;IACzB,MAAM,EAAE,MAAM,CAAC;CAChB,GAAG,CACA;IACE,IAAI,EAAE,YAAY,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;CACpB,GACD;IACE,IAAI,EAAE,kBAAkB,CAAC;IACzB,gBAAgB,EAAE,MAAM,CAAC;CAC1B,GACD;IACE,IAAI,EAAE,aAAa,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;CACrB,CACJ,CAAC;AAEF,MAAM,WAAW,OAAO;IACtB,EAAE,EAAE,UAAU,CAAC;IACf,yBAAyB,EAAE,OAAO,CAAC;IACnC,OAAO,EAAE,GAAG,GAAG,SAAS,CAAC;IAGzB,KAAK,CAAC,IAAI,EAAE;QACV,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,SAAS,CAAC;QACrB,YAAY,CAAC,EAAE,GAAG,CAAC;QACnB,cAAc,EAAE,MAAM,GAAG,IAAI,CAAC;KAC/B,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IACnB,eAAe,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAC5E,aAAa,CACX,MAAM,EAAE,MAAM,GACb,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACzD,YAAY,IAAI,YAAY,GAAG,IAAI,CAAC;IACpC;;OAEG;IACH,mBAAmB,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC;CACtD;AAUD,MAAM,MAAM,SAAS,GAAG,OAAO,GAAG;IAMhC,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;CAC/D,CAAC;AA2DF,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,EAAE;IACjC,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,KAAK,OAAO,CAAC,SAAS,CAQtB,CAAC;AAOF,wBAAgB,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,QAGrD;AAGD,wBAAgB,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,EAAE,GAAG,QAGtD;AAGD,wBAAgB,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,EAAE,GAAG,QAGtD;AAID,wBAAgB,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,EAAE,GAAG,QAKrD;AAED,wBAAgB,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,EAAE,GAAG,QAItD;AAED;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAC9B,GAAG,EAAE,OAAO,EACZ,MAAM,EAAE,MAAM,EACd,kBAAkB,EAAE,WAAW,CAAC,kBAAkB,GACjD,WAAW,CAGb;AAOD,wBAAgB,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,QAYxD;AAED,wBAAgB,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,QAO1D;AAED,wBAAgB,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,QAOvD;AAGD,wBAAgB,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,QAO5D;AAED,wBAAgB,WAAW,CAAC,GAAG,EAAE,OAAO,QAKvC;AAGD,wBAAsB,iBAAiB,CACrC,GAAG,EAAE,OAAO,EACZ,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EACf,EAAE,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,iBAOvB"}