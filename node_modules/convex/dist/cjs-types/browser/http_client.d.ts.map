{"version": 3, "file": "http_client.d.ts", "sourceRoot": "", "sources": ["../../../src/browser/http_client.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAEjB,MAAM,kBAAkB,CAAC;AAS1B,OAAO,EAIL,MAAM,EACP,MAAM,cAAc,CAAC;AACtB,OAAO,EACL,cAAc,EAGf,MAAM,oBAAoB,CAAC;AAE5B,eAAO,MAAM,cAAc,MAAM,CAAC;AAClC,eAAO,MAAM,uBAAuB,MAAM,CAAC;AAI3C,eAAO,MAAM,sBAAsB,MAAM,CAAC;AAI1C,wBAAgB,QAAQ,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,KAAK,QAElD;AAED,MAAM,MAAM,mBAAmB,GAAG;IAChC;;;;;OAKG;IACH,SAAS,EAAE,OAAO,CAAC;CACpB,CAAC;AAEF;;;;;;;;;;GAUG;AACH,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAS;IACjC,OAAO,CAAC,IAAI,CAAC,CAAS;IACtB,OAAO,CAAC,SAAS,CAAC,CAAS;IAC3B,OAAO,CAAC,gBAAgB,CAAC,CAAkB;IAC3C,OAAO,CAAC,KAAK,CAAU;IACvB,OAAO,CAAC,YAAY,CAAC,CAAe;IACpC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,aAAa,CAKb;IACR,OAAO,CAAC,iBAAiB,CAAkB;IAE3C;;;;;;;;;;;;;;;OAeG;gBAED,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QACR,4BAA4B,CAAC,EAAE,OAAO,CAAC;QACvC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAC1B,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;IAqBH;;;;;OAKG;IACH,UAAU,IAAI,MAAM;IAIpB;;;;;OAKG;IACH,IAAI,GAAG,WAEN;IAED;;;;;;OAMG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM;IAuBrB;;OAEG;IACH,SAAS;IAuBT;;;;;;;;;;;;;;;;;;OAkBG;IACG,eAAe,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,CAAC,EAC5D,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,GAC/B,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAOvB,YAAY;YAOZ,iBAAiB;IAmB/B;;;;;;;OAOG;IACG,KAAK,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,CAAC,EAClD,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,GAC/B,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAKvB,UAAU;YAgEV,aAAa;YAmDb,oBAAoB;IAkBlC,OAAO,CAAC,eAAe;IAUvB;;;;;;;;OAQG;IACG,QAAQ,CAAC,QAAQ,SAAS,iBAAiB,CAAC,UAAU,CAAC,EAC3D,QAAQ,EAAE,QAAQ,EAClB,GAAG,IAAI,EAAE,cAAc,CAAC,QAAQ,EAAE,mBAAmB,CAAC,GACrD,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAYxC;;;;;;;OAOG;IACG,MAAM,CAAC,MAAM,SAAS,iBAAiB,CAAC,QAAQ,CAAC,EACrD,MAAM,EAAE,MAAM,EACd,GAAG,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,GAChC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;CAqHvC"}