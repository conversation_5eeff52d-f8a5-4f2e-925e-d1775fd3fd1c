import { Value } from "../../values/index.js";
export declare function canonicalizeUdfPath(udfPath: string): string;
/**
 * A string representing the name and arguments of a query.
 *
 * This is used by the {@link BaseConvexClient}.
 *
 * @public
 */
export type QueryToken = string;
export declare function serializePathAndArgs(udfPath: string, args: Record<string, Value>): QueryToken;
//# sourceMappingURL=udf_path_utils.d.ts.map