declare const markNames: readonly ["convexClientConstructed", "convexWebSocketOpen", "convexFirstMessageReceived"];
export type MarkName = (typeof markNames)[number];
export declare function mark(name: MarkN<PERSON>, sessionId: string): void;
export type MarkJson = {
    name: string;
    startTime: number;
};
export declare function getMarksReport(sessionId: string): MarkJson[];
export {};
//# sourceMappingURL=metrics.d.ts.map