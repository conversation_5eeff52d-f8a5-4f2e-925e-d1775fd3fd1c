{"version": 3, "file": "protocol.d.ts", "sourceRoot": "", "sources": ["../../../../src/browser/sync/protocol.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC;AAC7E,YAAY,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC;AAC7E,OAAO,EAAE,SAAS,EAAU,MAAM,uBAAuB,CAAC;AAC1D,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAElC;;GAEG;AAEH,wBAAgB,SAAS,CAAC,OAAO,EAAE,UAAU,GAAG,GAAG,CAGlD;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,UAAU,CAG9C;AAED,wBAAgB,kBAAkB,CAChC,OAAO,EAAE,oBAAoB,GAC5B,aAAa,CAiCf;AAED,wBAAgB,mBAAmB,CACjC,OAAO,EAAE,aAAa,GACrB,oBAAoB,CAwBtB;AAED,KAAK,GAAG,GAAG,IAAI,CAAC;AAChB,KAAK,UAAU,GAAG,MAAM,CAAC;AAEzB;;GAEG;AACH,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC;AAE7B,MAAM,MAAM,eAAe,GAAG,MAAM,CAAC;AAErC,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC;AAE/B,MAAM,MAAM,eAAe,GAAG,MAAM,CAAC;AAErC;;;;;;;;;;;;GAYG;AACH,MAAM,MAAM,YAAY,GAAG,MAAM,GAAG,IAAI,CAAC;AAEzC;;GAEG;AAEH,KAAK,OAAO,GAAG;IACb,IAAI,EAAE,SAAS,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,eAAe,EAAE,MAAM,CAAC;IACxB,eAAe,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,oBAAoB,CAAC,EAAE,EAAE,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,QAAQ,GAAG;IACrB,IAAI,EAAE,KAAK,CAAC;IACZ,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,SAAS,EAAE,CAAC;IAClB,OAAO,CAAC,EAAE,YAAY,CAAC;CAKxB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG;IACxB,IAAI,EAAE,QAAQ,CAAC;IACf,OAAO,EAAE,OAAO,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC,IAAI,EAAE,gBAAgB,CAAC;IACvB,WAAW,EAAE,eAAe,CAAC;IAC7B,UAAU,EAAE,eAAe,CAAC;IAC5B,aAAa,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,EAAE,CAAC;CAC3C,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC5B,IAAI,EAAE,UAAU,CAAC;IACjB,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,SAAS,EAAE,CAAC;IAGlB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC1B,IAAI,EAAE,QAAQ,CAAC;IACf,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,SAAS,EAAE,CAAC;IAGlB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,IAAI,EAAE,cAAc,CAAC;IACrB,SAAS,EAAE,OAAO,CAAC;IACnB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,eAAe,CAAC;IAC7B,aAAa,CAAC,EAAE,sBAAsB,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,YAAY,GACpB,mBAAmB,GACnB;IACE,IAAI,EAAE,cAAc,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,eAAe,CAAC;CAC9B,GACD;IACE,IAAI,EAAE,cAAc,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,eAAe,CAAC;CAC9B,CAAC;AAEN,MAAM,MAAM,KAAK,GAAG;IAClB,IAAI,EAAE,OAAO,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,GAAG,CAAC;CACZ,CAAC;AACF,MAAM,MAAM,aAAa,GACrB,OAAO,GACP,YAAY,GACZ,oBAAoB,GACpB,eAAe,GACf,aAAa,GACb,KAAK,CAAC;AAEV,KAAK,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,sBAAsB,CAAC,GAAG;IAC5D,oBAAoB,CAAC,EAAE,SAAS,CAAC;CAClC,CAAC;AAEF,KAAK,oBAAoB,GACrB,cAAc,GACd,YAAY,GACZ,oBAAoB,GACpB,eAAe,GACf,aAAa,GACb,KAAK,CAAC;AAEV;;GAEG;AACH,MAAM,MAAM,EAAE,GAAG,GAAG,CAAC;AACrB,KAAK,SAAS,GAAG,UAAU,CAAC;AAC5B,KAAK,QAAQ,GAAG,MAAM,EAAE,CAAC;AAEzB,MAAM,MAAM,YAAY,GAAG;IACzB,QAAQ,EAAE,eAAe,CAAC;IAC1B,EAAE,EAAE,EAAE,CAAC;IACP,QAAQ,EAAE,eAAe,CAAC;CAC3B,CAAC;AACF,KAAK,mBAAmB,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG;IAAE,EAAE,EAAE,SAAS,CAAA;CAAE,CAAC;AAExE,KAAK,iBAAiB,GAClB;IACE,IAAI,EAAE,cAAc,CAAC;IACrB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,EAAE,SAAS,CAAC;IACjB,QAAQ,EAAE,QAAQ,CAAC;IACnB,OAAO,EAAE,YAAY,CAAC;CACvB,GACD;IACE,IAAI,EAAE,aAAa,CAAC;IACpB,OAAO,EAAE,OAAO,CAAC;IACjB,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,QAAQ,CAAC;IACnB,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,YAAY,CAAC;CACvB,GACD;IACE,IAAI,EAAE,cAAc,CAAC;IACrB,OAAO,EAAE,OAAO,CAAC;CAClB,CAAC;AAEN,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,YAAY,CAAC;IACnB,YAAY,EAAE,YAAY,CAAC;IAC3B,UAAU,EAAE,YAAY,CAAC;IACzB,aAAa,EAAE,iBAAiB,EAAE,CAAC;CACpC,CAAC;AAEF,KAAK,eAAe,GAAG;IACrB,IAAI,EAAE,kBAAkB,CAAC;IACzB,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,IAAI,CAAC;IACd,MAAM,EAAE,SAAS,CAAC;IAClB,EAAE,EAAE,EAAE,CAAC;IACP,QAAQ,EAAE,QAAQ,CAAC;CACpB,CAAC;AACF,KAAK,cAAc,GAAG;IACpB,IAAI,EAAE,kBAAkB,CAAC;IACzB,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,KAAK,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,QAAQ,CAAC;IACnB,SAAS,CAAC,EAAE,SAAS,CAAC;CACvB,CAAC;AACF,MAAM,MAAM,gBAAgB,GAAG,eAAe,GAAG,cAAc,CAAC;AAChE,KAAK,aAAa,GAAG;IACnB,IAAI,EAAE,gBAAgB,CAAC;IACvB,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,IAAI,CAAC;IACd,MAAM,EAAE,SAAS,CAAC;IAClB,QAAQ,EAAE,QAAQ,CAAC;CACpB,CAAC;AACF,KAAK,YAAY,GAAG;IAClB,IAAI,EAAE,gBAAgB,CAAC;IACvB,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,KAAK,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,QAAQ,CAAC;IACnB,SAAS,CAAC,EAAE,SAAS,CAAC;CACvB,CAAC;AACF,MAAM,MAAM,cAAc,GAAG,aAAa,GAAG,YAAY,CAAC;AAC1D,MAAM,MAAM,SAAS,GAAG;IACtB,IAAI,EAAE,WAAW,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,eAAe,CAAC;IAI7B,mBAAmB,EAAE,OAAO,CAAC;CAC9B,CAAC;AACF,KAAK,UAAU,GAAG;IAChB,IAAI,EAAE,YAAY,CAAC;IACnB,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AACF,KAAK,IAAI,GAAG;IACV,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEF,MAAM,MAAM,aAAa,GACrB,UAAU,GACV,gBAAgB,GAChB,cAAc,GACd,UAAU,GACV,SAAS,GACT,IAAI,CAAC;AAET,KAAK,iBAAiB,GAAG,IAAI,CAAC,UAAU,EAAE,cAAc,GAAG,YAAY,CAAC,GAAG;IACzE,YAAY,EAAE,mBAAmB,CAAC;IAClC,UAAU,EAAE,mBAAmB,CAAC;CACjC,CAAC;AACF,KAAK,sBAAsB,GAAG,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG;IAAE,EAAE,EAAE,SAAS,CAAA;CAAE,CAAC;AAC9E,KAAK,uBAAuB,GAAG,cAAc,GAAG,sBAAsB,CAAC;AAEvE,KAAK,oBAAoB,GACrB,iBAAiB,GACjB,uBAAuB,GACvB,cAAc,GACd,UAAU,GACV,SAAS,GACT,IAAI,CAAC"}