export declare class Long {
    low: number;
    high: number;
    __isUnsignedLong__: boolean;
    static isLong(obj: Long): boolean;
    constructor(low: number, high: number);
    static fromBytesLE(bytes: number[]): Long;
    toBytesLE(): number[];
    static fromNumber(value: number): Long;
    toString(): string;
    equals(other: Long): boolean;
    notEquals(other: Long): boolean;
    comp(other: Long): 1 | 0 | -1;
    lessThanOrEqual(other: Long): boolean;
    static fromValue(val: any): Long;
}
//# sourceMappingURL=long.d.ts.map