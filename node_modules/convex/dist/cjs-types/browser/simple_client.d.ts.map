{"version": 3, "file": "simple_client.d.ts", "sourceRoot": "", "sources": ["../../../src/browser/simple_client.ts"], "names": [], "mappings": "AACA,OAAO,EACL,gBAAgB,EAChB,uBAAuB,EACvB,eAAe,EAGhB,MAAM,YAAY,CAAC;AACpB,OAAO,EACL,YAAY,EACZ,iBAAiB,EACjB,kBAAkB,EACnB,MAAM,oBAAoB,CAAC;AAE5B,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AAOpE,eAAe;AACf,wBAAgB,8BAA8B,CAAC,EAAE,EAAE,OAAO,SAAS,QAElE;AAED,MAAM,MAAM,mBAAmB,GAAG,uBAAuB,GAAG;IAC1D;;;;OAIG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;;;;OAKG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;CACjC,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI;IAC3B,2HAA2H;IAC3H,IAAI,IAAI,CAAC;IACT,2HAA2H;IAC3H,WAAW,IAAI,IAAI,CAAC;IACpB,gFAAgF;IAChF,eAAe,IAAI,CAAC,GAAG,SAAS,CAAC;CAGlC,CAAC;AAEF;;;;;;;;;;;;;;GAcG;AACH,qBAAa,YAAY;IACvB,OAAO,CAAC,SAAS,CAAiB;IAClC,OAAO,CAAC,OAAO,CAA+B;IAE9C,OAAO,CAAC,sCAAsC,CAEhC;IACd,OAAO,CAAC,OAAO,CAAU;IACzB,OAAO,CAAC,SAAS,CAAU;IAC3B;;OAEG;IACH,IAAI,MAAM,IAAI,OAAO,CAEpB;IACD,IAAI,MAAM,IAAI,gBAAgB,CAG7B;IACD,IAAI,QAAQ,IAAI,OAAO,CAEtB;IAED;;;;OAIG;gBACS,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,mBAAwB;IA8B9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,QAAQ,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,CAAC,EAC/C,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EACzB,QAAQ,EAAE,CAAC,MAAM,EAAE,kBAAkB,CAAC,KAAK,CAAC,KAAK,OAAO,EACxD,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,OAAO,GAC9B,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IAsEpC,OAAO,CAAC,iCAAiC;IAKzC,OAAO,CAAC,gBAAgB;IAIlB,KAAK;IAQX;;;;;;;OAOG;IACH,OAAO,CACL,UAAU,EAAE,gBAAgB,EAC5B,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,OAAO,KAAK,IAAI;IA+D/C;;;;;;;;OAQG;IACG,QAAQ,CAAC,QAAQ,SAAS,iBAAiB,CAAC,UAAU,CAAC,EAC3D,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAE,YAAY,CAAC,QAAQ,CAAC,EAC5B,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAKjD;;;;;;;OAOG;IACG,MAAM,CAAC,MAAM,SAAS,iBAAiB,CAAC,QAAQ,CAAC,EACrD,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,GACzB,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;IAK/C;;;;;;;OAOG;IACG,KAAK,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,CAAC,EAClD,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,GACnB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;CAsB1C"}