{"version": 3, "sources": ["../../../../src/cli/lib/deploymentSelection.ts"], "sourcesContent": ["import { BigBrainAuth, Context, logVerbose } from \"../../bundler/context.js\";\nimport {\n  AccountRequiredDeploymentType,\n  DeploymentType,\n  fetchTeamAndProjectForKey,\n} from \"./api.js\";\nimport { readProjectConfig } from \"./config.js\";\nimport {\n  deploymentNameFromAdminKeyOrCrash,\n  deploymentTypeFromAdminKey,\n  getDeploymentTypeFromConfiguredDeployment,\n  isAnonymousDeployment,\n  isPreviewDeployKey,\n  isProjectKey,\n  stripDeploymentTypePrefix,\n} from \"./deployment.js\";\nimport { buildEnvironment } from \"./envvars.js\";\nimport { readGlobalConfig } from \"./utils/globalConfig.js\";\nimport {\n  CONVEX_DEPLOYMENT_ENV_VAR_NAME,\n  CONVEX_DEPLOY_KEY_ENV_VAR_NAME,\n  CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME,\n  CONVEX_SELF_HOSTED_URL_VAR_NAME,\n  ENV_VAR_FILE_PATH,\n  bigBrainAPI,\n} from \"./utils/utils.js\";\nimport * as dotenv from \"dotenv\";\n\n// ----------------------------------------------------------------------------\n// Big Brain Auth\n// ----------------------------------------------------------------------------\n\n/**\n * The auth header can be a few different things:\n * * An access token (corresponds to device authorization, usually stored in `~/.convex/config.json`)\n * * A preview deploy key (set via the `CONVEX_DEPLOY_KEY` environment variable)\n * * A project key (set via the `CONVEX_DEPLOY_KEY` environment variable)\n *\n * Project keys take precedence over the the access token.\n *\n * We check for the `CONVEX_DEPLOY_KEY` in the `--env-file` if it's provided.\n * Otherwise, we check in the `.env` and `.env.local` files.\n *\n * If we later prompt for log in, we need to call `ctx.setBigBrainAuthHeader` to\n * update the value.\n *\n * @param ctx\n * @param envFile\n * @returns\n */\nexport async function initializeBigBrainAuth(\n  ctx: Context,\n  initialArgs: {\n    url?: string;\n    adminKey?: string;\n    envFile?: string;\n  },\n): Promise<void> {\n  if (initialArgs.url !== undefined && initialArgs.adminKey !== undefined) {\n    // Do not check any env vars if `url` and `adminKey` are specified via CLI\n    ctx._updateBigBrainAuth(\n      getBigBrainAuth(ctx, {\n        previewDeployKey: null,\n        projectKey: null,\n      }),\n    );\n    return;\n  }\n  if (initialArgs.envFile !== undefined) {\n    const existingFile = ctx.fs.exists(initialArgs.envFile)\n      ? ctx.fs.readUtf8File(initialArgs.envFile)\n      : null;\n    if (existingFile === null) {\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem or env vars\",\n        printedMessage: \"env file does not exist\",\n      });\n    }\n    const config = dotenv.parse(existingFile);\n    const deployKey = config[CONVEX_DEPLOY_KEY_ENV_VAR_NAME];\n    if (deployKey !== undefined) {\n      const bigBrainAuth = getBigBrainAuth(ctx, {\n        previewDeployKey: isPreviewDeployKey(deployKey) ? deployKey : null,\n        projectKey: isProjectKey(deployKey) ? deployKey : null,\n      });\n      ctx._updateBigBrainAuth(bigBrainAuth);\n    }\n    return;\n  }\n  dotenv.config({ path: ENV_VAR_FILE_PATH });\n  dotenv.config();\n  const deployKey = process.env[CONVEX_DEPLOY_KEY_ENV_VAR_NAME];\n  if (deployKey !== undefined) {\n    const bigBrainAuth = getBigBrainAuth(ctx, {\n      previewDeployKey: isPreviewDeployKey(deployKey) ? deployKey : null,\n      projectKey: isProjectKey(deployKey) ? deployKey : null,\n    });\n    ctx._updateBigBrainAuth(bigBrainAuth);\n    return;\n  }\n  ctx._updateBigBrainAuth(\n    getBigBrainAuth(ctx, {\n      previewDeployKey: null,\n      projectKey: null,\n    }),\n  );\n  return;\n}\n\nexport async function updateBigBrainAuthAfterLogin(\n  ctx: Context,\n  accessToken: string,\n) {\n  const existingAuth = ctx.bigBrainAuth();\n  if (existingAuth !== null && existingAuth.kind === \"projectKey\") {\n    logVerbose(\n      ctx,\n      `Ignoring update to big brain auth since project key takes precedence`,\n    );\n    return;\n  }\n  ctx._updateBigBrainAuth({\n    accessToken: accessToken,\n    kind: \"accessToken\",\n    header: `Bearer ${accessToken}`,\n  });\n}\n\nexport async function clearBigBrainAuth(ctx: Context) {\n  ctx._updateBigBrainAuth(null);\n}\n\nfunction getBigBrainAuth(\n  ctx: Context,\n  opts: {\n    previewDeployKey: string | null;\n    projectKey: string | null;\n  },\n): BigBrainAuth | null {\n  if (process.env.CONVEX_OVERRIDE_ACCESS_TOKEN) {\n    return {\n      accessToken: process.env.CONVEX_OVERRIDE_ACCESS_TOKEN,\n      kind: \"accessToken\",\n      header: `Bearer ${process.env.CONVEX_OVERRIDE_ACCESS_TOKEN}`,\n    };\n  }\n  if (opts.projectKey !== null) {\n    // Project keys take precedence over global config.\n    return {\n      header: `Bearer ${opts.projectKey}`,\n      kind: \"projectKey\",\n      projectKey: opts.projectKey,\n    };\n  }\n  const globalConfig = readGlobalConfig(ctx);\n  if (globalConfig) {\n    return {\n      kind: \"accessToken\",\n      header: `Bearer ${globalConfig.accessToken}`,\n      accessToken: globalConfig.accessToken,\n    };\n  }\n  if (opts.previewDeployKey !== null) {\n    return {\n      header: `Bearer ${opts.previewDeployKey}`,\n      kind: \"previewDeployKey\",\n      previewDeployKey: opts.previewDeployKey,\n    };\n  }\n  return null;\n}\n\n// ----------------------------------------------------------------------------\n// Deployment Selection\n// ----------------------------------------------------------------------------\n/**\n * Our CLI has logic to select which deployment to act on.\n *\n * We first check whether we're targeting a deployment within a project, or if we\n * know exactly which deployment to act on (e.g. in the case of self-hosting).\n *\n * We also special case preview deploys since the presence of a preview deploy key\n * triggers different behavior in `npx convex deploy`.\n *\n * Most commands will immediately compute the deployment selection, and then combine\n * that with any relevant CLI flags to figure out which deployment to talk to.\n *\n * Different commands do different things (e.g. `dev` will allow you to create a new project,\n * `deploy` has different behavior for preview deploys)\n *\n * This should be kept in sync with `initializeBigBrainAuth` since environment variables\n * like `CONVEX_DEPLOY_KEY` are used for both deployment selection and auth.\n */\nexport type DeploymentSelection =\n  | {\n      kind: \"existingDeployment\";\n      deploymentToActOn: {\n        url: string;\n        adminKey: string;\n        deploymentFields: {\n          deploymentName: string;\n          deploymentType: DeploymentType;\n          projectSlug: string;\n          teamSlug: string;\n        } | null;\n        source: \"selfHosted\" | \"deployKey\" | \"cliArgs\";\n      };\n    }\n  | {\n      kind: \"deploymentWithinProject\";\n      targetProject: ProjectSelection;\n    }\n  | {\n      kind: \"preview\";\n      previewDeployKey: string;\n    }\n  | {\n      kind: \"chooseProject\";\n    }\n  | {\n      kind: \"anonymous\";\n      deploymentName: string | null;\n    };\n\nexport type ProjectSelection =\n  | {\n      kind: \"teamAndProjectSlugs\";\n      teamSlug: string;\n      projectSlug: string;\n    }\n  | {\n      kind: \"deploymentName\";\n      deploymentName: string;\n      deploymentType: AccountRequiredDeploymentType | null;\n    }\n  | {\n      kind: \"projectDeployKey\";\n      projectDeployKey: string;\n    };\n\nexport async function getDeploymentSelection(\n  ctx: Context,\n  cliArgs: {\n    url?: string;\n    adminKey?: string;\n    envFile?: string;\n  },\n): Promise<DeploymentSelection> {\n  const metadata = await _getDeploymentSelection(ctx, cliArgs);\n  logDeploymentSelection(ctx, metadata);\n  return metadata;\n}\n\nfunction logDeploymentSelection(ctx: Context, selection: DeploymentSelection) {\n  switch (selection.kind) {\n    case \"existingDeployment\": {\n      logVerbose(\n        ctx,\n        `Existing deployment: ${selection.deploymentToActOn.url} ${selection.deploymentToActOn.source}`,\n      );\n      break;\n    }\n    case \"deploymentWithinProject\": {\n      logVerbose(\n        ctx,\n        `Deployment within project: ${prettyProjectSelection(selection.targetProject)}`,\n      );\n      break;\n    }\n    case \"preview\": {\n      logVerbose(ctx, `Preview deploy key`);\n      break;\n    }\n    case \"chooseProject\": {\n      logVerbose(ctx, `Choose project`);\n      break;\n    }\n    case \"anonymous\": {\n      logVerbose(\n        ctx,\n        `Anonymous, has selected deployment?: ${selection.deploymentName !== null}`,\n      );\n      break;\n    }\n    default: {\n      const _exhaustivenessCheck: never = selection;\n      logVerbose(ctx, `Unknown deployment selection`);\n    }\n  }\n  return null;\n}\n\nfunction prettyProjectSelection(selection: ProjectSelection) {\n  switch (selection.kind) {\n    case \"teamAndProjectSlugs\": {\n      return `Team and project slugs: ${selection.teamSlug} ${selection.projectSlug}`;\n    }\n    case \"deploymentName\": {\n      return `Deployment name: ${selection.deploymentName}`;\n    }\n    case \"projectDeployKey\": {\n      return `Project deploy key`;\n    }\n    default: {\n      const _exhaustivenessCheck: never = selection;\n      return `Unknown`;\n    }\n  }\n}\n\nasync function _getDeploymentSelection(\n  ctx: Context,\n  cliArgs: {\n    url?: string;\n    adminKey?: string;\n    envFile?: string;\n  },\n): Promise<DeploymentSelection> {\n  /*\n   - url + adminKey specified via CLI\n   - Do not check any env vars (including ones relevant for auth)\n  */\n  if (cliArgs.url && cliArgs.adminKey) {\n    return {\n      kind: \"existingDeployment\",\n      deploymentToActOn: {\n        url: cliArgs.url,\n        adminKey: cliArgs.adminKey,\n        deploymentFields: null,\n        source: \"cliArgs\",\n      },\n    };\n  }\n\n  if (cliArgs.envFile) {\n    // If an `--env-file` is specified, it must contain enough information for both auth and deployment selection.\n    logVerbose(ctx, `Checking env file: ${cliArgs.envFile}`);\n    const existingFile = ctx.fs.exists(cliArgs.envFile)\n      ? ctx.fs.readUtf8File(cliArgs.envFile)\n      : null;\n    if (existingFile === null) {\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem or env vars\",\n        printedMessage: \"env file does not exist\",\n      });\n    }\n    const config = dotenv.parse(existingFile);\n    const result = await getDeploymentSelectionFromEnv(ctx, (name) =>\n      config[name] === undefined || config[name] === \"\" ? null : config[name],\n    );\n    if (result.kind === \"unknown\") {\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem or env vars\",\n        printedMessage:\n          `env file \\`${cliArgs.envFile}\\` did not contain environment variables for a Convex deployment. ` +\n          `Expected \\`${CONVEX_DEPLOY_KEY_ENV_VAR_NAME}\\`, \\`${CONVEX_DEPLOYMENT_ENV_VAR_NAME}\\`, or both \\`${CONVEX_SELF_HOSTED_URL_VAR_NAME}\\` and \\`${CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME}\\` to be set.`,\n      });\n    }\n    return result.metadata;\n  }\n  // start with .env.local (but doesn't override existing)\n  dotenv.config({ path: ENV_VAR_FILE_PATH });\n  // for variables not already set, use .env values\n  dotenv.config();\n  const result = await getDeploymentSelectionFromEnv(ctx, (name) => {\n    const value = process.env[name];\n    if (value === undefined || value === \"\") {\n      return null;\n    }\n    return value;\n  });\n  if (result.kind !== \"unknown\") {\n    return result.metadata;\n  }\n  // none of these?\n\n  // Check the `convex.json` for a configured team and project\n  const { projectConfig } = await readProjectConfig(ctx);\n  if (projectConfig.team !== undefined && projectConfig.project !== undefined) {\n    return {\n      kind: \"deploymentWithinProject\",\n      targetProject: {\n        kind: \"teamAndProjectSlugs\",\n        teamSlug: projectConfig.team,\n        projectSlug: projectConfig.project,\n      },\n    };\n  }\n\n  // Check if they're logged in\n  const isLoggedIn = ctx.bigBrainAuth() !== null;\n  if (!isLoggedIn && shouldAllowAnonymousDevelopment()) {\n    return {\n      kind: \"anonymous\",\n      deploymentName: null,\n    };\n  }\n\n  // Choose a project interactively later\n  return {\n    kind: \"chooseProject\",\n  };\n}\n\nasync function getDeploymentSelectionFromEnv(\n  ctx: Context,\n  getEnv: (name: string) => string | null,\n): Promise<\n  { kind: \"success\"; metadata: DeploymentSelection } | { kind: \"unknown\" }\n> {\n  const deployKey = getEnv(CONVEX_DEPLOY_KEY_ENV_VAR_NAME);\n  if (deployKey !== null) {\n    const deployKeyType = isPreviewDeployKey(deployKey)\n      ? \"preview\"\n      : isProjectKey(deployKey)\n        ? \"project\"\n        : \"deployment\";\n    switch (deployKeyType) {\n      case \"preview\": {\n        // `CONVEX_DEPLOY_KEY` is set to a preview deploy key so this takes precedence over anything else.\n        // At the moment, we don't verify that there aren't other env vars that would also be used for deployment selection (e.g. `CONVEX_DEPLOYMENT`)\n        return {\n          kind: \"success\",\n          metadata: {\n            kind: \"preview\",\n            previewDeployKey: deployKey,\n          },\n        };\n      }\n      case \"project\": {\n        // `CONVEX_DEPLOY_KEY` is set to a project deploy key.\n        // Commands can select any deployment within the project. At the moment we don't check for other env vars (e.g. `CONVEX_DEPLOYMENT`)\n        return {\n          kind: \"success\",\n          metadata: {\n            kind: \"deploymentWithinProject\",\n            targetProject: {\n              kind: \"projectDeployKey\",\n              projectDeployKey: deployKey,\n            },\n          },\n        };\n      }\n      case \"deployment\": {\n        // `CONVEX_DEPLOY_KEY` is set to a deployment's deploy key.\n        // Deploy to this deployment -- selectors like `--prod` / `--preview-name` will be ignored.\n        // At the moment, we don't verify that there aren't other env vars that would also be used for deployment selection (e.g. `CONVEX_DEPLOYMENT`)\n        const deploymentName = await deploymentNameFromAdminKeyOrCrash(\n          ctx,\n          deployKey,\n        );\n        const deploymentType = deploymentTypeFromAdminKey(deployKey);\n        // We cannot derive the deployment URL from the deploy key, because it\n        // might be a custom domain. Ask big brain for the URL.\n        const url = await bigBrainAPI({\n          ctx,\n          method: \"POST\",\n          url: \"deployment/url_for_key\",\n          data: {\n            deployKey: deployKey,\n          },\n        });\n        const slugs = await fetchTeamAndProjectForKey(ctx, deployKey);\n        return {\n          kind: \"success\",\n          metadata: {\n            kind: \"existingDeployment\",\n            deploymentToActOn: {\n              url: url,\n              adminKey: deployKey,\n              deploymentFields: {\n                deploymentName: deploymentName,\n                deploymentType: deploymentType,\n                teamSlug: slugs.team,\n                projectSlug: slugs.project,\n              },\n              source: \"deployKey\",\n            },\n          },\n        };\n      }\n      default: {\n        const _exhaustivenessCheck: never = deployKeyType;\n        return ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Unexpected deploy key type: ${deployKeyType as any}`,\n        });\n      }\n    }\n  }\n  // Throw a nice error if we're in something like a CI environment where we need a `CONVEX_DEPLOY_KEY`\n  await checkIfBuildEnvironmentExpectsConvexDeployKey(ctx);\n\n  const convexDeployment = getEnv(CONVEX_DEPLOYMENT_ENV_VAR_NAME);\n  const selfHostedUrl = getEnv(CONVEX_SELF_HOSTED_URL_VAR_NAME);\n  const selfHostedAdminKey = getEnv(CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME);\n\n  if (selfHostedUrl !== null && selfHostedAdminKey !== null) {\n    if (convexDeployment !== null) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem or env vars\",\n        printedMessage: `${CONVEX_DEPLOYMENT_ENV_VAR_NAME} must not be set when ${CONVEX_SELF_HOSTED_URL_VAR_NAME} and ${CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME} are set`,\n      });\n    }\n    return {\n      kind: \"success\",\n      metadata: {\n        kind: \"existingDeployment\",\n        deploymentToActOn: {\n          url: selfHostedUrl,\n          adminKey: selfHostedAdminKey,\n          deploymentFields: null,\n          source: \"selfHosted\",\n        },\n      },\n    };\n  }\n\n  if (convexDeployment !== null) {\n    if (selfHostedUrl !== null || selfHostedAdminKey !== null) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem or env vars\",\n        printedMessage: `${CONVEX_SELF_HOSTED_URL_VAR_NAME} and ${CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME} must not be set when ${CONVEX_DEPLOYMENT_ENV_VAR_NAME} is set`,\n      });\n    }\n    const targetDeploymentType =\n      getDeploymentTypeFromConfiguredDeployment(convexDeployment);\n    const targetDeploymentName = stripDeploymentTypePrefix(convexDeployment);\n    const isAnonymous = isAnonymousDeployment(targetDeploymentName);\n    if (isAnonymous) {\n      if (!shouldAllowAnonymousDevelopment()) {\n        return {\n          kind: \"unknown\",\n        };\n      }\n      return {\n        kind: \"success\",\n        metadata: {\n          kind: \"anonymous\",\n          deploymentName: targetDeploymentName,\n        },\n      };\n    }\n    // Commands can select a deployment within the project that this deployment belongs to.\n    return {\n      kind: \"success\",\n      metadata: {\n        kind: \"deploymentWithinProject\",\n        targetProject: {\n          kind: \"deploymentName\",\n          deploymentName: targetDeploymentName,\n          deploymentType: targetDeploymentType,\n        },\n      },\n    };\n  }\n\n  return { kind: \"unknown\" };\n}\n\nasync function checkIfBuildEnvironmentExpectsConvexDeployKey(ctx: Context) {\n  const buildEnvironmentExpectsConvexDeployKey = buildEnvironment();\n  if (buildEnvironmentExpectsConvexDeployKey) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage:\n        `${buildEnvironmentExpectsConvexDeployKey} build environment detected but ${CONVEX_DEPLOY_KEY_ENV_VAR_NAME} is not set. ` +\n        `Set this environment variable to deploy from this environment. See https://docs.convex.dev/production/hosting`,\n    });\n  }\n}\n\n/**\n * Used for things like `npx convex docs` where we want to best effort extract a deployment name\n * but don't do the full deployment selection logic.\n */\nexport const deploymentNameFromSelection = (\n  selection: DeploymentSelection,\n): string | null => {\n  return deploymentNameAndTypeFromSelection(selection)?.name ?? null;\n};\n\nexport const deploymentNameAndTypeFromSelection = (\n  selection: DeploymentSelection,\n): { name: string | null; type: string | null } | null => {\n  switch (selection.kind) {\n    case \"existingDeployment\": {\n      return {\n        name:\n          selection.deploymentToActOn.deploymentFields?.deploymentName ?? null,\n        type:\n          selection.deploymentToActOn.deploymentFields?.deploymentType ?? null,\n      };\n    }\n    case \"deploymentWithinProject\": {\n      return selection.targetProject.kind === \"deploymentName\"\n        ? {\n            name: selection.targetProject.deploymentName,\n            type: selection.targetProject.deploymentType,\n          }\n        : null;\n    }\n    case \"preview\": {\n      return null;\n    }\n    case \"chooseProject\": {\n      return null;\n    }\n    case \"anonymous\": {\n      return null;\n    }\n  }\n  const _exhaustivenessCheck: never = selection;\n  return null;\n};\n\nexport const shouldAllowAnonymousDevelopment = (): boolean => {\n  // Kill switch / temporary opt out\n  if (process.env.CONVEX_ALLOW_ANONYMOUS === \"false\") {\n    return false;\n  }\n  return true;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAkD;AAClD,iBAIO;AACP,oBAAkC;AAClC,wBAQO;AACP,qBAAiC;AACjC,0BAAiC;AACjC,mBAOO;AACP,aAAwB;AAwBxB,eAAsB,uBACpB,KACA,aAKe;AACf,MAAI,YAAY,QAAQ,UAAa,YAAY,aAAa,QAAW;AAEvE,QAAI;AAAA,MACF,gBAAgB,KAAK;AAAA,QACnB,kBAAkB;AAAA,QAClB,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA;AAAA,EACF;AACA,MAAI,YAAY,YAAY,QAAW;AACrC,UAAM,eAAe,IAAI,GAAG,OAAO,YAAY,OAAO,IAClD,IAAI,GAAG,aAAa,YAAY,OAAO,IACvC;AACJ,QAAI,iBAAiB,MAAM;AACzB,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AACA,UAAM,SAAS,OAAO,MAAM,YAAY;AACxC,UAAMA,aAAY,OAAO,2CAA8B;AACvD,QAAIA,eAAc,QAAW;AAC3B,YAAM,eAAe,gBAAgB,KAAK;AAAA,QACxC,sBAAkB,sCAAmBA,UAAS,IAAIA,aAAY;AAAA,QAC9D,gBAAY,gCAAaA,UAAS,IAAIA,aAAY;AAAA,MACpD,CAAC;AACD,UAAI,oBAAoB,YAAY;AAAA,IACtC;AACA;AAAA,EACF;AACA,SAAO,OAAO,EAAE,MAAM,+BAAkB,CAAC;AACzC,SAAO,OAAO;AACd,QAAM,YAAY,QAAQ,IAAI,2CAA8B;AAC5D,MAAI,cAAc,QAAW;AAC3B,UAAM,eAAe,gBAAgB,KAAK;AAAA,MACxC,sBAAkB,sCAAmB,SAAS,IAAI,YAAY;AAAA,MAC9D,gBAAY,gCAAa,SAAS,IAAI,YAAY;AAAA,IACpD,CAAC;AACD,QAAI,oBAAoB,YAAY;AACpC;AAAA,EACF;AACA,MAAI;AAAA,IACF,gBAAgB,KAAK;AAAA,MACnB,kBAAkB;AAAA,MAClB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACA;AACF;AAEA,eAAsB,6BACpB,KACA,aACA;AACA,QAAM,eAAe,IAAI,aAAa;AACtC,MAAI,iBAAiB,QAAQ,aAAa,SAAS,cAAc;AAC/D;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA;AAAA,EACF;AACA,MAAI,oBAAoB;AAAA,IACtB;AAAA,IACA,MAAM;AAAA,IACN,QAAQ,UAAU,WAAW;AAAA,EAC/B,CAAC;AACH;AAEA,eAAsB,kBAAkB,KAAc;AACpD,MAAI,oBAAoB,IAAI;AAC9B;AAEA,SAAS,gBACP,KACA,MAIqB;AACrB,MAAI,QAAQ,IAAI,8BAA8B;AAC5C,WAAO;AAAA,MACL,aAAa,QAAQ,IAAI;AAAA,MACzB,MAAM;AAAA,MACN,QAAQ,UAAU,QAAQ,IAAI,4BAA4B;AAAA,IAC5D;AAAA,EACF;AACA,MAAI,KAAK,eAAe,MAAM;AAE5B,WAAO;AAAA,MACL,QAAQ,UAAU,KAAK,UAAU;AAAA,MACjC,MAAM;AAAA,MACN,YAAY,KAAK;AAAA,IACnB;AAAA,EACF;AACA,QAAM,mBAAe,sCAAiB,GAAG;AACzC,MAAI,cAAc;AAChB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ,UAAU,aAAa,WAAW;AAAA,MAC1C,aAAa,aAAa;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,KAAK,qBAAqB,MAAM;AAClC,WAAO;AAAA,MACL,QAAQ,UAAU,KAAK,gBAAgB;AAAA,MACvC,MAAM;AAAA,MACN,kBAAkB,KAAK;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AAsEA,eAAsB,uBACpB,KACA,SAK8B;AAC9B,QAAM,WAAW,MAAM,wBAAwB,KAAK,OAAO;AAC3D,yBAAuB,KAAK,QAAQ;AACpC,SAAO;AACT;AAEA,SAAS,uBAAuB,KAAc,WAAgC;AAC5E,UAAQ,UAAU,MAAM;AAAA,IACtB,KAAK,sBAAsB;AACzB;AAAA,QACE;AAAA,QACA,wBAAwB,UAAU,kBAAkB,GAAG,IAAI,UAAU,kBAAkB,MAAM;AAAA,MAC/F;AACA;AAAA,IACF;AAAA,IACA,KAAK,2BAA2B;AAC9B;AAAA,QACE;AAAA,QACA,8BAA8B,uBAAuB,UAAU,aAAa,CAAC;AAAA,MAC/E;AACA;AAAA,IACF;AAAA,IACA,KAAK,WAAW;AACd,qCAAW,KAAK,oBAAoB;AACpC;AAAA,IACF;AAAA,IACA,KAAK,iBAAiB;AACpB,qCAAW,KAAK,gBAAgB;AAChC;AAAA,IACF;AAAA,IACA,KAAK,aAAa;AAChB;AAAA,QACE;AAAA,QACA,wCAAwC,UAAU,mBAAmB,IAAI;AAAA,MAC3E;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,uBAA8B;AACpC,qCAAW,KAAK,8BAA8B;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,uBAAuB,WAA6B;AAC3D,UAAQ,UAAU,MAAM;AAAA,IACtB,KAAK,uBAAuB;AAC1B,aAAO,2BAA2B,UAAU,QAAQ,IAAI,UAAU,WAAW;AAAA,IAC/E;AAAA,IACA,KAAK,kBAAkB;AACrB,aAAO,oBAAoB,UAAU,cAAc;AAAA,IACrD;AAAA,IACA,KAAK,oBAAoB;AACvB,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,YAAM,uBAA8B;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,eAAe,wBACb,KACA,SAK8B;AAK9B,MAAI,QAAQ,OAAO,QAAQ,UAAU;AACnC,WAAO;AAAA,MACL,MAAM;AAAA,MACN,mBAAmB;AAAA,QACjB,KAAK,QAAQ;AAAA,QACb,UAAU,QAAQ;AAAA,QAClB,kBAAkB;AAAA,QAClB,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,SAAS;AAEnB,mCAAW,KAAK,sBAAsB,QAAQ,OAAO,EAAE;AACvD,UAAM,eAAe,IAAI,GAAG,OAAO,QAAQ,OAAO,IAC9C,IAAI,GAAG,aAAa,QAAQ,OAAO,IACnC;AACJ,QAAI,iBAAiB,MAAM;AACzB,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AACA,UAAM,SAAS,OAAO,MAAM,YAAY;AACxC,UAAMC,UAAS,MAAM;AAAA,MAA8B;AAAA,MAAK,CAAC,SACvD,OAAO,IAAI,MAAM,UAAa,OAAO,IAAI,MAAM,KAAK,OAAO,OAAO,IAAI;AAAA,IACxE;AACA,QAAIA,QAAO,SAAS,WAAW;AAC7B,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE,cAAc,QAAQ,OAAO,gFACf,2CAA8B,SAAS,2CAA8B,iBAAiB,4CAA+B,YAAY,kDAAqC;AAAA,MACxL,CAAC;AAAA,IACH;AACA,WAAOA,QAAO;AAAA,EAChB;AAEA,SAAO,OAAO,EAAE,MAAM,+BAAkB,CAAC;AAEzC,SAAO,OAAO;AACd,QAAM,SAAS,MAAM,8BAA8B,KAAK,CAAC,SAAS;AAChE,UAAM,QAAQ,QAAQ,IAAI,IAAI;AAC9B,QAAI,UAAU,UAAa,UAAU,IAAI;AACvC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AACD,MAAI,OAAO,SAAS,WAAW;AAC7B,WAAO,OAAO;AAAA,EAChB;AAIA,QAAM,EAAE,cAAc,IAAI,UAAM,iCAAkB,GAAG;AACrD,MAAI,cAAc,SAAS,UAAa,cAAc,YAAY,QAAW;AAC3E,WAAO;AAAA,MACL,MAAM;AAAA,MACN,eAAe;AAAA,QACb,MAAM;AAAA,QACN,UAAU,cAAc;AAAA,QACxB,aAAa,cAAc;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAGA,QAAM,aAAa,IAAI,aAAa,MAAM;AAC1C,MAAI,CAAC,cAAc,gCAAgC,GAAG;AACpD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB;AAAA,EACF;AAGA,SAAO;AAAA,IACL,MAAM;AAAA,EACR;AACF;AAEA,eAAe,8BACb,KACA,QAGA;AACA,QAAM,YAAY,OAAO,2CAA8B;AACvD,MAAI,cAAc,MAAM;AACtB,UAAM,oBAAgB,sCAAmB,SAAS,IAC9C,gBACA,gCAAa,SAAS,IACpB,YACA;AACN,YAAQ,eAAe;AAAA,MACrB,KAAK,WAAW;AAGd,eAAO;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,YACR,MAAM;AAAA,YACN,kBAAkB;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,MACA,KAAK,WAAW;AAGd,eAAO;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,YACR,MAAM;AAAA,YACN,eAAe;AAAA,cACb,MAAM;AAAA,cACN,kBAAkB;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,KAAK,cAAc;AAIjB,cAAM,iBAAiB,UAAM;AAAA,UAC3B;AAAA,UACA;AAAA,QACF;AACA,cAAM,qBAAiB,8CAA2B,SAAS;AAG3D,cAAM,MAAM,UAAM,0BAAY;AAAA,UAC5B;AAAA,UACA,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,UACF;AAAA,QACF,CAAC;AACD,cAAM,QAAQ,UAAM,sCAA0B,KAAK,SAAS;AAC5D,eAAO;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,YACR,MAAM;AAAA,YACN,mBAAmB;AAAA,cACjB;AAAA,cACA,UAAU;AAAA,cACV,kBAAkB;AAAA,gBAChB;AAAA,gBACA;AAAA,gBACA,UAAU,MAAM;AAAA,gBAChB,aAAa,MAAM;AAAA,cACrB;AAAA,cACA,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,uBAA8B;AACpC,eAAO,IAAI,MAAM;AAAA,UACf,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,+BAA+B,aAAoB;AAAA,QACrE,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,QAAM,8CAA8C,GAAG;AAEvD,QAAM,mBAAmB,OAAO,2CAA8B;AAC9D,QAAM,gBAAgB,OAAO,4CAA+B;AAC5D,QAAM,qBAAqB,OAAO,kDAAqC;AAEvE,MAAI,kBAAkB,QAAQ,uBAAuB,MAAM;AACzD,QAAI,qBAAqB,MAAM;AAC7B,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,GAAG,2CAA8B,yBAAyB,4CAA+B,QAAQ,kDAAqC;AAAA,MACxJ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,QACR,MAAM;AAAA,QACN,mBAAmB;AAAA,UACjB,KAAK;AAAA,UACL,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,qBAAqB,MAAM;AAC7B,QAAI,kBAAkB,QAAQ,uBAAuB,MAAM;AACzD,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,GAAG,4CAA+B,QAAQ,kDAAqC,yBAAyB,2CAA8B;AAAA,MACxJ,CAAC;AAAA,IACH;AACA,UAAM,2BACJ,6DAA0C,gBAAgB;AAC5D,UAAM,2BAAuB,6CAA0B,gBAAgB;AACvE,UAAM,kBAAc,yCAAsB,oBAAoB;AAC9D,QAAI,aAAa;AACf,UAAI,CAAC,gCAAgC,GAAG;AACtC,eAAO;AAAA,UACL,MAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,MAAM;AAAA,UACN,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,QACR,MAAM;AAAA,QACN,eAAe;AAAA,UACb,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,EAAE,MAAM,UAAU;AAC3B;AAEA,eAAe,8CAA8C,KAAc;AACzE,QAAM,6CAAyC,iCAAiB;AAChE,MAAI,wCAAwC;AAC1C,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE,GAAG,sCAAsC,mCAAmC,2CAA8B;AAAA,IAE9G,CAAC;AAAA,EACH;AACF;AAMO,MAAM,8BAA8B,CACzC,cACkB;AAClB,SAAO,mCAAmC,SAAS,GAAG,QAAQ;AAChE;AAEO,MAAM,qCAAqC,CAChD,cACwD;AACxD,UAAQ,UAAU,MAAM;AAAA,IACtB,KAAK,sBAAsB;AACzB,aAAO;AAAA,QACL,MACE,UAAU,kBAAkB,kBAAkB,kBAAkB;AAAA,QAClE,MACE,UAAU,kBAAkB,kBAAkB,kBAAkB;AAAA,MACpE;AAAA,IACF;AAAA,IACA,KAAK,2BAA2B;AAC9B,aAAO,UAAU,cAAc,SAAS,mBACpC;AAAA,QACE,MAAM,UAAU,cAAc;AAAA,QAC9B,MAAM,UAAU,cAAc;AAAA,MAChC,IACA;AAAA,IACN;AAAA,IACA,KAAK,WAAW;AACd,aAAO;AAAA,IACT;AAAA,IACA,KAAK,iBAAiB;AACpB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,aAAa;AAChB,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,uBAA8B;AACpC,SAAO;AACT;AAEO,MAAM,kCAAkC,MAAe;AAE5D,MAAI,QAAQ,IAAI,2BAA2B,SAAS;AAClD,WAAO;AAAA,EACT;AACA,SAAO;AACT;", "names": ["deployKey", "result"]}