{"version": 3, "sources": ["../../../../src/cli/lib/envvars.ts"], "sourcesContent": ["/**\n * Help the developer store the CONVEX_URL environment variable.\n */\nimport chalk from \"chalk\";\nimport * as dotenv from \"dotenv\";\n\nimport { Context, logWarning } from \"../../bundler/context.js\";\nimport { loadPackageJson } from \"./utils/utils.js\";\n\nconst _FRAMEWORKS = [\n  \"create-react-app\",\n  \"Next.js\",\n  \"Vite\",\n  \"Remix\",\n  \"SvelteKit\",\n  \"Expo\",\n  \"TanStackStart\",\n] as const;\ntype Framework = (typeof _FRAMEWORKS)[number];\n\ntype ConvexUrlWriteConfig = {\n  envFile: string;\n  envVar: string;\n  existingFileContent: string | null;\n} | null;\n\nexport async function writeConvexUrlToEnvFile(\n  ctx: Context,\n  value: string,\n): Promise<ConvexUrlWriteConfig> {\n  const writeConfig = await envVarWriteConfig(ctx, value);\n\n  if (writeConfig === null) {\n    return null;\n  }\n\n  const { envFile, envVar, existingFileContent } = writeConfig;\n  const modified = changedEnvVarFile({\n    existingFileContent,\n    envVarName: envVar,\n    envVarValue: value,\n    commentAfterValue: null,\n    commentOnPreviousLine: null,\n  })!;\n  ctx.fs.writeUtf8File(envFile, modified);\n  return writeConfig;\n}\n\nexport function changedEnvVarFile({\n  existingFileContent,\n  envVarName,\n  envVarValue,\n  commentAfterValue,\n  commentOnPreviousLine,\n}: {\n  existingFileContent: string | null;\n  envVarName: string;\n  envVarValue: string;\n  commentAfterValue: string | null;\n  commentOnPreviousLine: string | null;\n}): string | null {\n  const varAssignment = `${envVarName}=${envVarValue}${\n    commentAfterValue === null ? \"\" : ` # ${commentAfterValue}`\n  }`;\n  const commentOnPreviousLineWithLineBreak =\n    commentOnPreviousLine === null ? \"\" : `${commentOnPreviousLine}\\n`;\n  if (existingFileContent === null) {\n    return `${commentOnPreviousLineWithLineBreak}${varAssignment}\\n`;\n  }\n  const config = dotenv.parse(existingFileContent);\n  const existing = config[envVarName];\n  if (existing === envVarValue) {\n    return null;\n  }\n  if (existing !== undefined) {\n    return existingFileContent.replace(\n      getEnvVarRegex(envVarName),\n      `${varAssignment}`,\n    );\n  } else {\n    const doubleLineBreak = existingFileContent.endsWith(\"\\n\") ? \"\\n\" : \"\\n\\n\";\n    return (\n      existingFileContent +\n      doubleLineBreak +\n      commentOnPreviousLineWithLineBreak +\n      varAssignment +\n      \"\\n\"\n    );\n  }\n}\n\nexport function getEnvVarRegex(envVarName: string) {\n  return new RegExp(`^${envVarName}.*$`, \"m\");\n}\n\nexport async function suggestedEnvVarName(ctx: Context): Promise<{\n  detectedFramework?: Framework;\n  envVar: string;\n}> {\n  // no package.json, that's fine, just guess\n  if (!ctx.fs.exists(\"package.json\")) {\n    return {\n      envVar: \"CONVEX_URL\",\n    };\n  }\n\n  const packages = await loadPackageJson(ctx);\n\n  // Is it create-react-app?\n  const isCreateReactApp = \"react-scripts\" in packages;\n  if (isCreateReactApp) {\n    return {\n      detectedFramework: \"create-react-app\",\n      envVar: \"REACT_APP_CONVEX_URL\",\n    };\n  }\n\n  const isNextJs = \"next\" in packages;\n  if (isNextJs) {\n    return {\n      detectedFramework: \"Next.js\",\n      envVar: \"NEXT_PUBLIC_CONVEX_URL\",\n    };\n  }\n\n  const isExpo = \"expo\" in packages;\n  if (isExpo) {\n    return {\n      detectedFramework: \"Expo\",\n      envVar: \"EXPO_PUBLIC_CONVEX_URL\",\n    };\n  }\n\n  const isRemix = \"@remix-run/dev\" in packages;\n  if (isRemix) {\n    return {\n      detectedFramework: \"Remix\",\n      envVar: \"CONVEX_URL\",\n    };\n  }\n\n  const isSvelteKit = \"@sveltejs/kit\" in packages;\n  if (isSvelteKit) {\n    return {\n      detectedFramework: \"SvelteKit\",\n      envVar: \"PUBLIC_CONVEX_URL\",\n    };\n  }\n\n  // Vite is a dependency of a lot of things; vite appearing in dependencies is not a strong indicator.\n  const isVite = \"vite\" in packages;\n\n  if (isVite) {\n    return {\n      detectedFramework: \"Vite\",\n      envVar: \"VITE_CONVEX_URL\",\n    };\n  }\n\n  // TanStackStart currently supports VITE_FOO for browser-side envvars.\n  const isTanStackStart =\n    \"@tanstack/start\" in packages || \"@tanstack/react-start\" in packages;\n\n  if (isTanStackStart) {\n    return {\n      detectedFramework: \"TanStackStart\",\n      envVar: \"VITE_CONVEX_URL\",\n    };\n  }\n\n  return {\n    envVar: \"CONVEX_URL\",\n  };\n}\n\nasync function envVarWriteConfig(\n  ctx: Context,\n  value: string | null,\n): Promise<ConvexUrlWriteConfig> {\n  const { detectedFramework, envVar } = await suggestedEnvVarName(ctx);\n\n  const { envFile, existing } = suggestedDevEnvFile(ctx, detectedFramework);\n\n  if (!existing) {\n    return { envFile, envVar, existingFileContent: null };\n  }\n\n  const existingFileContent = ctx.fs.readUtf8File(envFile);\n  const config = dotenv.parse(existingFileContent);\n\n  const matching = Object.keys(config).filter((key) => EXPECTED_NAMES.has(key));\n  if (matching.length > 1) {\n    logWarning(\n      ctx,\n      chalk.yellow(\n        `Found multiple CONVEX_URL environment variables in ${envFile} so cannot update automatically.`,\n      ),\n    );\n    return null;\n  }\n  if (matching.length === 1) {\n    const [existingEnvVar, oldValue] = [matching[0], config[matching[0]]];\n    if (oldValue === value) {\n      return null;\n    }\n    if (\n      oldValue !== \"\" &&\n      Object.values(config).filter((v) => v === oldValue).length !== 1\n    ) {\n      logWarning(\n        ctx,\n        chalk.yellow(`Can't safely modify ${envFile}, please edit manually.`),\n      );\n      return null;\n    }\n    return { envFile, envVar: existingEnvVar, existingFileContent };\n  }\n  return { envFile, envVar, existingFileContent };\n}\n\nfunction suggestedDevEnvFile(\n  ctx: Context,\n  framework?: Framework,\n): {\n  existing: boolean;\n  envFile: string;\n} {\n  // If a .env.local file exists, that's unequivocally the right file\n  if (ctx.fs.exists(\".env.local\")) {\n    return {\n      existing: true,\n      envFile: \".env.local\",\n    };\n  }\n\n  // Remix is on team \"don't commit the .env file,\" so .env is for dev.\n  if (framework === \"Remix\") {\n    return {\n      existing: ctx.fs.exists(\".env\"),\n      envFile: \".env\",\n    };\n  }\n\n  // The most dev-looking env file that exists, or .env.local\n  return {\n    existing: ctx.fs.exists(\".env.local\"),\n    envFile: \".env.local\",\n  };\n}\n\nconst EXPECTED_NAMES = new Set([\n  \"CONVEX_URL\",\n  \"PUBLIC_CONVEX_URL\",\n  \"NEXT_PUBLIC_CONVEX_URL\",\n  \"VITE_CONVEX_URL\",\n  \"REACT_APP_CONVEX_URL\",\n  \"EXPO_PUBLIC_CONVEX_URL\",\n]);\n\n// Crash or warn on\n// CONVEX_DEPLOY_KEY=project:me:new-project|eyABCD0= npx convex\n// which parses as\n// CONVEX_DEPLOY_KEY=project:me:new-project | eyABCD0='' npx convex\n// when what was intended was\n// CONVEX_DEPLOY_KEY=project:me:new-project|eyABCD0= npx convex\n// This only fails so catastrophically when the key ends with `=`.\nexport async function detectSuspiciousEnvironmentVariables(\n  ctx: Context,\n  ignoreSuspiciousEnvVars = false,\n) {\n  for (const [key, value] of Object.entries(process.env)) {\n    if (value === \"\" && key.startsWith(\"ey\")) {\n      try {\n        // add a \"=\" to the end and try to base64 decode (expected format of Convex keys)\n        const decoded = JSON.parse(\n          Buffer.from(key + \"=\", \"base64\").toString(\"utf8\"),\n        );\n        // Only parseable v2 tokens to be sure this is a Convex token before complaining.\n        if (!(\"v2\" in decoded)) {\n          continue;\n        }\n      } catch {\n        continue;\n      }\n\n      if (ignoreSuspiciousEnvVars) {\n        logWarning(\n          ctx,\n          `ignoring suspicious environment variable ${key}, did you mean to use quotes like CONVEX_DEPLOY_KEY='...'?`,\n        );\n      } else {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Quotes are required around environment variable values by your shell: CONVEX_DEPLOY_KEY='project:name:project|${key.slice(0, 4)}...${key.slice(key.length - 4)}=' npx convex dev`,\n        });\n      }\n    }\n  }\n}\n\nexport function buildEnvironment(): string | boolean {\n  return process.env.VERCEL\n    ? \"Vercel\"\n    : process.env.NETLIFY\n      ? \"Netlify\"\n      : false;\n}\n\nexport function gitBranchFromEnvironment(): string | null {\n  if (process.env.VERCEL) {\n    // https://vercel.com/docs/projects/environment-variables/system-environment-variables\n    return process.env.VERCEL_GIT_COMMIT_REF ?? null;\n  }\n  if (process.env.NETLIFY) {\n    // https://docs.netlify.com/configure-builds/environment-variables/\n    return process.env.HEAD ?? null;\n  }\n\n  if (process.env.CI) {\n    // https://docs.github.com/en/actions/learn-github-actions/variables\n    // https://docs.gitlab.com/ee/ci/variables/predefined_variables.html\n    return (\n      process.env.GITHUB_HEAD_REF ?? process.env.CI_COMMIT_REF_NAME ?? null\n    );\n  }\n\n  return null;\n}\n\nexport function isNonProdBuildEnvironment(): boolean {\n  if (process.env.VERCEL) {\n    // https://vercel.com/docs/projects/environment-variables/system-environment-variables\n    return process.env.VERCEL_ENV !== \"production\";\n  }\n  if (process.env.NETLIFY) {\n    // https://docs.netlify.com/configure-builds/environment-variables/\n    return process.env.CONTEXT !== \"production\";\n  }\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,mBAAkB;AAClB,aAAwB;AAExB,qBAAoC;AACpC,mBAAgC;AAEhC,MAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AASA,eAAsB,wBACpB,KACA,OAC+B;AAC/B,QAAM,cAAc,MAAM,kBAAkB,KAAK,KAAK;AAEtD,MAAI,gBAAgB,MAAM;AACxB,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,SAAS,QAAQ,oBAAoB,IAAI;AACjD,QAAM,WAAW,kBAAkB;AAAA,IACjC;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,EACzB,CAAC;AACD,MAAI,GAAG,cAAc,SAAS,QAAQ;AACtC,SAAO;AACT;AAEO,SAAS,kBAAkB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAMkB;AAChB,QAAM,gBAAgB,GAAG,UAAU,IAAI,WAAW,GAChD,sBAAsB,OAAO,KAAK,MAAM,iBAAiB,EAC3D;AACA,QAAM,qCACJ,0BAA0B,OAAO,KAAK,GAAG,qBAAqB;AAAA;AAChE,MAAI,wBAAwB,MAAM;AAChC,WAAO,GAAG,kCAAkC,GAAG,aAAa;AAAA;AAAA,EAC9D;AACA,QAAM,SAAS,OAAO,MAAM,mBAAmB;AAC/C,QAAM,WAAW,OAAO,UAAU;AAClC,MAAI,aAAa,aAAa;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,aAAa,QAAW;AAC1B,WAAO,oBAAoB;AAAA,MACzB,eAAe,UAAU;AAAA,MACzB,GAAG,aAAa;AAAA,IAClB;AAAA,EACF,OAAO;AACL,UAAM,kBAAkB,oBAAoB,SAAS,IAAI,IAAI,OAAO;AACpE,WACE,sBACA,kBACA,qCACA,gBACA;AAAA,EAEJ;AACF;AAEO,SAAS,eAAe,YAAoB;AACjD,SAAO,IAAI,OAAO,IAAI,UAAU,OAAO,GAAG;AAC5C;AAEA,eAAsB,oBAAoB,KAGvC;AAED,MAAI,CAAC,IAAI,GAAG,OAAO,cAAc,GAAG;AAClC,WAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,QAAM,WAAW,UAAM,8BAAgB,GAAG;AAG1C,QAAM,mBAAmB,mBAAmB;AAC5C,MAAI,kBAAkB;AACpB,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,QAAM,WAAW,UAAU;AAC3B,MAAI,UAAU;AACZ,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,QAAM,SAAS,UAAU;AACzB,MAAI,QAAQ;AACV,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,QAAM,UAAU,oBAAoB;AACpC,MAAI,SAAS;AACX,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,QAAM,cAAc,mBAAmB;AACvC,MAAI,aAAa;AACf,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,QAAQ;AAAA,IACV;AAAA,EACF;AAGA,QAAM,SAAS,UAAU;AAEzB,MAAI,QAAQ;AACV,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,QAAQ;AAAA,IACV;AAAA,EACF;AAGA,QAAM,kBACJ,qBAAqB,YAAY,2BAA2B;AAE9D,MAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,mBAAmB;AAAA,MACnB,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,SAAO;AAAA,IACL,QAAQ;AAAA,EACV;AACF;AAEA,eAAe,kBACb,KACA,OAC+B;AAC/B,QAAM,EAAE,mBAAmB,OAAO,IAAI,MAAM,oBAAoB,GAAG;AAEnE,QAAM,EAAE,SAAS,SAAS,IAAI,oBAAoB,KAAK,iBAAiB;AAExE,MAAI,CAAC,UAAU;AACb,WAAO,EAAE,SAAS,QAAQ,qBAAqB,KAAK;AAAA,EACtD;AAEA,QAAM,sBAAsB,IAAI,GAAG,aAAa,OAAO;AACvD,QAAM,SAAS,OAAO,MAAM,mBAAmB;AAE/C,QAAM,WAAW,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,QAAQ,eAAe,IAAI,GAAG,CAAC;AAC5E,MAAI,SAAS,SAAS,GAAG;AACvB;AAAA,MACE;AAAA,MACA,aAAAA,QAAM;AAAA,QACJ,sDAAsD,OAAO;AAAA,MAC/D;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,SAAS,WAAW,GAAG;AACzB,UAAM,CAAC,gBAAgB,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO,SAAS,CAAC,CAAC,CAAC;AACpE,QAAI,aAAa,OAAO;AACtB,aAAO;AAAA,IACT;AACA,QACE,aAAa,MACb,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,MAAM,MAAM,QAAQ,EAAE,WAAW,GAC/D;AACA;AAAA,QACE;AAAA,QACA,aAAAA,QAAM,OAAO,uBAAuB,OAAO,yBAAyB;AAAA,MACtE;AACA,aAAO;AAAA,IACT;AACA,WAAO,EAAE,SAAS,QAAQ,gBAAgB,oBAAoB;AAAA,EAChE;AACA,SAAO,EAAE,SAAS,QAAQ,oBAAoB;AAChD;AAEA,SAAS,oBACP,KACA,WAIA;AAEA,MAAI,IAAI,GAAG,OAAO,YAAY,GAAG;AAC/B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS;AAAA,IACX;AAAA,EACF;AAGA,MAAI,cAAc,SAAS;AACzB,WAAO;AAAA,MACL,UAAU,IAAI,GAAG,OAAO,MAAM;AAAA,MAC9B,SAAS;AAAA,IACX;AAAA,EACF;AAGA,SAAO;AAAA,IACL,UAAU,IAAI,GAAG,OAAO,YAAY;AAAA,IACpC,SAAS;AAAA,EACX;AACF;AAEA,MAAM,iBAAiB,oBAAI,IAAI;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AASD,eAAsB,qCACpB,KACA,0BAA0B,OAC1B;AACA,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,GAAG,GAAG;AACtD,QAAI,UAAU,MAAM,IAAI,WAAW,IAAI,GAAG;AACxC,UAAI;AAEF,cAAM,UAAU,KAAK;AAAA,UACnB,OAAO,KAAK,MAAM,KAAK,QAAQ,EAAE,SAAS,MAAM;AAAA,QAClD;AAEA,YAAI,EAAE,QAAQ,UAAU;AACtB;AAAA,QACF;AAAA,MACF,QAAQ;AACN;AAAA,MACF;AAEA,UAAI,yBAAyB;AAC3B;AAAA,UACE;AAAA,UACA,4CAA4C,GAAG;AAAA,QACjD;AAAA,MACF,OAAO;AACL,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,iHAAiH,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,QACjL,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,mBAAqC;AACnD,SAAO,QAAQ,IAAI,SACf,WACA,QAAQ,IAAI,UACV,YACA;AACR;AAEO,SAAS,2BAA0C;AACxD,MAAI,QAAQ,IAAI,QAAQ;AAEtB,WAAO,QAAQ,IAAI,yBAAyB;AAAA,EAC9C;AACA,MAAI,QAAQ,IAAI,SAAS;AAEvB,WAAO,QAAQ,IAAI,QAAQ;AAAA,EAC7B;AAEA,MAAI,QAAQ,IAAI,IAAI;AAGlB,WACE,QAAQ,IAAI,mBAAmB,QAAQ,IAAI,sBAAsB;AAAA,EAErE;AAEA,SAAO;AACT;AAEO,SAAS,4BAAqC;AACnD,MAAI,QAAQ,IAAI,QAAQ;AAEtB,WAAO,QAAQ,IAAI,eAAe;AAAA,EACpC;AACA,MAAI,QAAQ,IAAI,SAAS;AAEvB,WAAO,QAAQ,IAAI,YAAY;AAAA,EACjC;AACA,SAAO;AACT;", "names": ["chalk"]}