{"version": 3, "sources": ["../../../src/cli/configure.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport {\n  Context,\n  logFailure,\n  logFinishedStep,\n  logMessage,\n  logWarning,\n  showSpinner,\n} from \"../bundler/context.js\";\nimport {\n  DeploymentType,\n  DeploymentName,\n  fetchDeploymentCredentialsProvisioningDevOrProdMaybeThrows,\n  createProject,\n  DeploymentSelectionWithinProject,\n  loadSelectedDeploymentCredentials,\n  checkAccessToSelectedProject,\n  validateDeploymentSelectionForExistingDeployment,\n} from \"./lib/api.js\";\nimport {\n  configName,\n  readProjectConfig,\n  upgradeOldAuthInfoToAuthConfig,\n  writeProjectConfig,\n} from \"./lib/config.js\";\nimport {\n  DeploymentDetails,\n  eraseDeploymentEnvVar,\n  writeDeploymentEnvVar,\n} from \"./lib/deployment.js\";\nimport { finalizeConfiguration } from \"./lib/init.js\";\nimport {\n  CONVEX_DEPLOYMENT_ENV_VAR_NAME,\n  functionsDir,\n  hasProjects,\n  logAndHandleFetchError,\n  selectDevDeploymentType,\n  validateOrSelectProject,\n  validateOrSelectTeam,\n} from \"./lib/utils/utils.js\";\nimport { writeConvexUrlToEnvFile } from \"./lib/envvars.js\";\nimport path from \"path\";\nimport { projectDashboardUrl } from \"./lib/dashboard.js\";\nimport { doCodegen, doCodegenForNewProject } from \"./lib/codegen.js\";\nimport { handleLocalDeployment } from \"./lib/localDeployment/localDeployment.js\";\nimport {\n  promptOptions,\n  promptString,\n  promptYesNo,\n} from \"./lib/utils/prompts.js\";\nimport { readGlobalConfig } from \"./lib/utils/globalConfig.js\";\nimport {\n  DeploymentSelection,\n  ProjectSelection,\n  deploymentNameFromSelection,\n  shouldAllowAnonymousDevelopment,\n} from \"./lib/deploymentSelection.js\";\nimport { ensureLoggedIn } from \"./lib/login.js\";\nimport { handleAnonymousDeployment } from \"./lib/localDeployment/anonymous.js\";\ntype DeploymentCredentials = {\n  url: string;\n  adminKey: string;\n};\n\ntype ChosenConfiguration =\n  // `--configure new`\n  | \"new\"\n  // `--configure existing`\n  | \"existing\"\n  // `--configure`\n  | \"ask\"\n  // `--configure` was not specified\n  | null;\n\ntype ConfigureCmdOptions = {\n  selectionWithinProject: DeploymentSelectionWithinProject;\n  prod: boolean;\n  localOptions: {\n    ports?: {\n      cloud: number;\n      site: number;\n    };\n    backendVersion?: string | undefined;\n    dashboardVersion?: string | undefined;\n    forceUpgrade: boolean;\n  };\n  team?: string | undefined;\n  project?: string | undefined;\n  devDeployment?: \"cloud\" | \"local\" | undefined;\n  local?: boolean | undefined;\n  cloud?: boolean | undefined;\n  url?: string | undefined;\n  adminKey?: string | undefined;\n  envFile?: string | undefined;\n  overrideAuthUrl?: string | undefined;\n  overrideAuthClient?: string | undefined;\n  overrideAuthUsername?: string | undefined;\n  overrideAuthPassword?: string | undefined;\n};\n\n/**\n * As of writing, this is used by:\n * - `npx convex dev`\n * - `npx convex codegen`\n *\n * But is not used by `npx convex deploy` or other commands.\n */\nexport async function deploymentCredentialsOrConfigure(\n  ctx: Context,\n  deploymentSelection: DeploymentSelection,\n  chosenConfiguration: ChosenConfiguration,\n  cmdOptions: ConfigureCmdOptions,\n  partitionId?: number | undefined,\n): Promise<\n  DeploymentCredentials & {\n    deploymentFields: {\n      deploymentName: DeploymentName;\n      deploymentType: string;\n      projectSlug: string | null;\n      teamSlug: string | null;\n    } | null;\n  }\n> {\n  const selectedDeployment = await _deploymentCredentialsOrConfigure(\n    ctx,\n    deploymentSelection,\n    chosenConfiguration,\n    cmdOptions,\n    partitionId,\n  );\n\n  if (selectedDeployment.deploymentFields !== null) {\n    // Set the `CONVEX_DEPLOYMENT` env var + the `CONVEX_URL` env var\n    await updateEnvAndConfigForDeploymentSelection(\n      ctx,\n      {\n        url: selectedDeployment.url,\n        deploymentName: selectedDeployment.deploymentFields.deploymentName,\n        teamSlug: selectedDeployment.deploymentFields.teamSlug,\n        projectSlug: selectedDeployment.deploymentFields.projectSlug,\n        deploymentType: selectedDeployment.deploymentFields.deploymentType,\n      },\n      deploymentNameFromSelection(deploymentSelection),\n    );\n  } else {\n    // Clear the `CONVEX_DEPLOYMENT` env var + set the `CONVEX_URL` env var\n    await handleManuallySetUrlAndAdminKey(ctx, {\n      url: selectedDeployment.url,\n      adminKey: selectedDeployment.adminKey,\n    });\n  }\n  return {\n    url: selectedDeployment.url,\n    adminKey: selectedDeployment.adminKey,\n    deploymentFields: selectedDeployment.deploymentFields,\n  };\n}\n\nexport async function _deploymentCredentialsOrConfigure(\n  ctx: Context,\n  deploymentSelection: DeploymentSelection,\n  chosenConfiguration: ChosenConfiguration,\n  cmdOptions: ConfigureCmdOptions,\n  partitionId?: number | undefined,\n): Promise<\n  DeploymentCredentials & {\n    deploymentFields: {\n      deploymentName: DeploymentName;\n      deploymentType: DeploymentType;\n      projectSlug: string | null;\n      teamSlug: string | null;\n    } | null;\n  }\n> {\n  const config = readGlobalConfig(ctx);\n  const globallyForceCloud = !!config?.optOutOfLocalDevDeploymentsUntilBetaOver;\n  if (globallyForceCloud && cmdOptions.local) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage:\n        \"Can't specify --local when local deployments are disabled on this machine. Run `npx convex disable-local-deployments --undo-global` to allow use of --local.\",\n    });\n  }\n\n  switch (deploymentSelection.kind) {\n    case \"existingDeployment\":\n      await validateDeploymentSelectionForExistingDeployment(\n        ctx,\n        cmdOptions.selectionWithinProject,\n        deploymentSelection.deploymentToActOn.source,\n      );\n      if (deploymentSelection.deploymentToActOn.deploymentFields === null) {\n        // erase `CONVEX_DEPLOYMENT` from .env.local + set the url env var\n        await handleManuallySetUrlAndAdminKey(ctx, {\n          url: deploymentSelection.deploymentToActOn.url,\n          adminKey: deploymentSelection.deploymentToActOn.adminKey,\n        });\n      }\n      return {\n        url: deploymentSelection.deploymentToActOn.url,\n        adminKey: deploymentSelection.deploymentToActOn.adminKey,\n        deploymentFields:\n          deploymentSelection.deploymentToActOn.deploymentFields,\n      };\n    case \"chooseProject\": {\n      await ensureLoggedIn(ctx, {\n        overrideAuthUrl: cmdOptions.overrideAuthUrl,\n        overrideAuthClient: cmdOptions.overrideAuthClient,\n        overrideAuthUsername: cmdOptions.overrideAuthUsername,\n        overrideAuthPassword: cmdOptions.overrideAuthPassword,\n      });\n      return await handleChooseProject(\n        ctx,\n        chosenConfiguration,\n        {\n          globallyForceCloud,\n          partitionId,\n        },\n        cmdOptions,\n      );\n    }\n    case \"preview\":\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: \"Use `npx convex deploy` to use preview deployments.\",\n      });\n    case \"deploymentWithinProject\": {\n      return await handleDeploymentWithinProject(ctx, {\n        chosenConfiguration,\n        targetProject: deploymentSelection.targetProject,\n        cmdOptions,\n        globallyForceCloud,\n        partitionId,\n      });\n    }\n    case \"anonymous\": {\n      const hasAuth = ctx.bigBrainAuth() !== null;\n      if (hasAuth && deploymentSelection.deploymentName !== null) {\n        const shouldConfigure =\n          chosenConfiguration !== null ||\n          (await promptYesNo(ctx, {\n            message: `${CONVEX_DEPLOYMENT_ENV_VAR_NAME} is configured with deployment ${deploymentSelection.deploymentName}, which is not linked with your account. Would you like to choose a different project instead?`,\n          }));\n        if (!shouldConfigure) {\n          return await ctx.crash({\n            exitCode: 0,\n            errorType: \"fatal\",\n            printedMessage: `Run \\`npx convex login --link-deployments\\` first to link this deployment to your account, and then run \\`npx convex dev\\` again.`,\n          });\n        }\n        return await handleChooseProject(\n          ctx,\n          chosenConfiguration,\n          {\n            globallyForceCloud,\n            partitionId,\n          },\n          cmdOptions,\n        );\n      }\n      const alreadyHasConfiguredAnonymousDeployment =\n        deploymentSelection.deploymentName !== null &&\n        chosenConfiguration === null;\n      const shouldPromptForLogin = alreadyHasConfiguredAnonymousDeployment\n        ? \"no\"\n        : await promptOptions(ctx, {\n            message:\n              \"Welcome to Convex! Would you like to login to your account?\",\n            choices: [\n              {\n                name: \"Start without an account (run Convex locally)\",\n                value: \"no\",\n              },\n              { name: \"Login or create an account\", value: \"yes\" },\n            ],\n            default: \"no\",\n          });\n      if (shouldPromptForLogin === \"no\") {\n        const result = await handleAnonymousDeployment(ctx, {\n          chosenConfiguration,\n          deploymentName: deploymentSelection.deploymentName,\n          ...cmdOptions.localOptions,\n        });\n        return {\n          adminKey: result.adminKey,\n          url: result.deploymentUrl,\n          deploymentFields: {\n            deploymentName: result.deploymentName,\n            deploymentType: \"anonymous\",\n            projectSlug: null,\n            teamSlug: null,\n          },\n        };\n      }\n      return await handleChooseProject(\n        ctx,\n        chosenConfiguration,\n        {\n          globallyForceCloud,\n          partitionId,\n        },\n        cmdOptions,\n      );\n    }\n  }\n}\n\nasync function handleDeploymentWithinProject(\n  ctx: Context,\n  {\n    chosenConfiguration,\n    targetProject,\n    cmdOptions,\n    globallyForceCloud,\n    partitionId,\n  }: {\n    chosenConfiguration: ChosenConfiguration;\n    targetProject: ProjectSelection;\n    cmdOptions: ConfigureCmdOptions;\n    globallyForceCloud: boolean;\n    partitionId?: number | undefined;\n  },\n) {\n  const hasAuth = ctx.bigBrainAuth() !== null;\n  const loginMessage =\n    hasAuth && shouldAllowAnonymousDevelopment()\n      ? undefined\n      : `Tip: You can try out Convex without creating an account by clearing the ${CONVEX_DEPLOYMENT_ENV_VAR_NAME} environment variable.`;\n  await ensureLoggedIn(ctx, {\n    message: loginMessage,\n    overrideAuthUrl: cmdOptions.overrideAuthUrl,\n    overrideAuthClient: cmdOptions.overrideAuthClient,\n    overrideAuthUsername: cmdOptions.overrideAuthUsername,\n    overrideAuthPassword: cmdOptions.overrideAuthPassword,\n  });\n  if (chosenConfiguration !== null) {\n    const result = await handleChooseProject(\n      ctx,\n      chosenConfiguration,\n      {\n        globallyForceCloud,\n        partitionId,\n      },\n      cmdOptions,\n    );\n    return result;\n  }\n\n  const accessResult = await checkAccessToSelectedProject(ctx, targetProject);\n  if (accessResult.kind === \"noAccess\") {\n    logMessage(ctx, \"You don't have access to the selected project.\");\n    const result = await handleChooseProject(\n      ctx,\n      chosenConfiguration,\n      {\n        globallyForceCloud,\n        partitionId,\n      },\n      cmdOptions,\n    );\n    return result;\n  }\n\n  const selectedDeployment = await loadSelectedDeploymentCredentials(\n    ctx,\n    {\n      kind: \"deploymentWithinProject\",\n      targetProject,\n    },\n    cmdOptions.selectionWithinProject,\n    // We'll start running it below\n    { ensureLocalRunning: false },\n  );\n  if (\n    selectedDeployment.deploymentFields !== null &&\n    selectedDeployment.deploymentFields.deploymentType === \"local\"\n  ) {\n    // Start running the local backend\n    await handleLocalDeployment(ctx, {\n      teamSlug: selectedDeployment.deploymentFields.teamSlug!,\n      projectSlug: selectedDeployment.deploymentFields.projectSlug!,\n      forceUpgrade: cmdOptions.localOptions.forceUpgrade,\n      ports: cmdOptions.localOptions.ports,\n      backendVersion: cmdOptions.localOptions.backendVersion,\n    });\n  }\n  return {\n    url: selectedDeployment.url,\n    adminKey: selectedDeployment.adminKey,\n    deploymentFields: selectedDeployment.deploymentFields,\n  };\n}\n\nasync function handleChooseProject(\n  ctx: Context,\n  chosenConfiguration: ChosenConfiguration,\n  args: {\n    globallyForceCloud: boolean;\n    partitionId?: number | undefined;\n  },\n  cmdOptions: ConfigureCmdOptions,\n): Promise<\n  DeploymentCredentials & {\n    deploymentFields: {\n      deploymentName: DeploymentName;\n      deploymentType: DeploymentType;\n      projectSlug: string;\n      teamSlug: string;\n    };\n  }\n> {\n  await ensureLoggedIn(ctx, {\n    overrideAuthUrl: cmdOptions.overrideAuthUrl,\n    overrideAuthClient: cmdOptions.overrideAuthClient,\n    overrideAuthUsername: cmdOptions.overrideAuthUsername,\n    overrideAuthPassword: cmdOptions.overrideAuthPassword,\n  });\n  const project = await selectProject(ctx, chosenConfiguration, {\n    team: cmdOptions.team,\n    project: cmdOptions.project,\n    devDeployment: cmdOptions.devDeployment,\n    local: args.globallyForceCloud ? false : cmdOptions.local,\n    cloud: args.globallyForceCloud ? true : cmdOptions.cloud,\n    partitionId: args.partitionId,\n  });\n  // TODO complain about any non-default cmdOptions.localOptions here\n  // because we're ignoring them if this isn't a local development.\n\n  const deploymentOptions: DeploymentOptions =\n    cmdOptions.selectionWithinProject.kind === \"prod\"\n      ? { kind: \"prod\" }\n      : project.devDeployment === \"local\"\n        ? { kind: \"local\", ...cmdOptions.localOptions }\n        : { kind: \"dev\" };\n  const {\n    deploymentName,\n    deploymentUrl: url,\n    adminKey,\n  } = await ensureDeploymentProvisioned(ctx, {\n    teamSlug: project.teamSlug,\n    projectSlug: project.projectSlug,\n    deploymentOptions,\n    partitionId: args.partitionId,\n  });\n  return {\n    url,\n    adminKey,\n    deploymentFields: {\n      deploymentName,\n      deploymentType: deploymentOptions.kind,\n      projectSlug: project.projectSlug,\n      teamSlug: project.teamSlug,\n    },\n  };\n}\n\nexport async function handleManuallySetUrlAndAdminKey(\n  ctx: Context,\n  cmdOptions: { url: string; adminKey: string },\n) {\n  const { url, adminKey } = cmdOptions;\n  const didErase = await eraseDeploymentEnvVar(ctx);\n  if (didErase) {\n    logMessage(\n      ctx,\n      chalk.yellowBright(\n        `Removed the CONVEX_DEPLOYMENT environment variable from .env.local`,\n      ),\n    );\n  }\n  const envVarWrite = await writeConvexUrlToEnvFile(ctx, url);\n  if (envVarWrite !== null) {\n    logMessage(\n      ctx,\n      chalk.green(\n        `Saved the given --url as ${envVarWrite.envVar} to ${envVarWrite.envFile}`,\n      ),\n    );\n  }\n  return { url, adminKey };\n}\n\nexport async function selectProject(\n  ctx: Context,\n  chosenConfiguration: ChosenConfiguration,\n  cmdOptions: {\n    team?: string | undefined;\n    project?: string | undefined;\n    devDeployment?: \"cloud\" | \"local\" | undefined;\n    local?: boolean | undefined;\n    cloud?: boolean | undefined;\n    partitionId?: number;\n    defaultProjectName?: string | undefined;\n  },\n): Promise<{\n  teamSlug: string;\n  projectSlug: string;\n  devDeployment: \"cloud\" | \"local\";\n}> {\n  // Prompt the user to select a project.\n  const choice =\n    chosenConfiguration !== \"ask\" && chosenConfiguration !== null\n      ? chosenConfiguration\n      : await askToConfigure(ctx);\n  switch (choice) {\n    case \"new\":\n      return selectNewProject(ctx, chosenConfiguration, cmdOptions);\n    case \"existing\":\n      return selectExistingProject(ctx, chosenConfiguration, cmdOptions);\n    default:\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: \"No project selected.\",\n      });\n  }\n}\n\nconst cwd = path.basename(process.cwd());\nasync function selectNewProject(\n  ctx: Context,\n  chosenConfiguration: ChosenConfiguration,\n  config: {\n    team?: string | undefined;\n    project?: string | undefined;\n    devDeployment?: \"cloud\" | \"local\" | undefined;\n    cloud?: boolean | undefined;\n    local?: boolean | undefined;\n    partitionId?: number | undefined;\n    defaultProjectName?: string | undefined;\n  },\n) {\n  const { teamSlug: selectedTeam, chosen: didChooseBetweenTeams } =\n    await validateOrSelectTeam(ctx, config.team, \"Team:\");\n  let projectName: string = config.project || cwd;\n  let choseProjectInteractively = false;\n  if (!config.project) {\n    projectName = await promptString(ctx, {\n      message: \"Project name:\",\n      default: config.defaultProjectName || cwd,\n    });\n    choseProjectInteractively = true;\n  }\n\n  const { devDeployment } = await selectDevDeploymentType(ctx, {\n    chosenConfiguration,\n    newOrExisting: \"new\",\n    teamSlug: selectedTeam,\n    userHasChosenSomethingInteractively:\n      didChooseBetweenTeams || choseProjectInteractively,\n    projectSlug: undefined,\n    devDeploymentFromFlag: config.devDeployment,\n    forceDevDeployment: config.local\n      ? \"local\"\n      : config.cloud\n        ? \"cloud\"\n        : undefined,\n  });\n\n  showSpinner(ctx, \"Creating new Convex project...\");\n\n  let projectSlug, teamSlug, projectsRemaining;\n  try {\n    ({ projectSlug, teamSlug, projectsRemaining } = await createProject(ctx, {\n      teamSlug: selectedTeam,\n      projectName,\n      partitionId: config.partitionId,\n      // We have to create some deployment initially for a project.\n      deploymentTypeToProvision: devDeployment === \"local\" ? \"prod\" : \"dev\",\n    }));\n  } catch (err) {\n    logFailure(ctx, \"Unable to create project.\");\n    return await logAndHandleFetchError(ctx, err);\n  }\n  const teamMessage = didChooseBetweenTeams\n    ? \" in team \" + chalk.bold(teamSlug)\n    : \"\";\n  logFinishedStep(\n    ctx,\n    `Created project ${chalk.bold(\n      projectSlug,\n    )}${teamMessage}, manage it at ${chalk.bold(\n      projectDashboardUrl(teamSlug, projectSlug),\n    )}`,\n  );\n\n  if (projectsRemaining <= 2) {\n    logWarning(\n      ctx,\n      chalk.yellow.bold(\n        `Your account now has ${projectsRemaining} project${\n          projectsRemaining === 1 ? \"\" : \"s\"\n        } remaining.`,\n      ),\n    );\n  }\n\n  await doCodegenForNewProject(ctx);\n  return { teamSlug, projectSlug, devDeployment };\n}\n\nasync function selectExistingProject(\n  ctx: Context,\n  chosenConfiguration: ChosenConfiguration,\n  config: {\n    team?: string | undefined;\n    project?: string | undefined;\n    devDeployment?: \"cloud\" | \"local\" | undefined;\n    local?: boolean | undefined;\n    cloud?: boolean | undefined;\n  },\n): Promise<{\n  teamSlug: string;\n  projectSlug: string;\n  devDeployment: \"cloud\" | \"local\";\n}> {\n  const { teamSlug, chosen } = await validateOrSelectTeam(\n    ctx,\n    config.team,\n    \"Team:\",\n  );\n\n  const projectSlug = await validateOrSelectProject(\n    ctx,\n    config.project,\n    teamSlug,\n    \"Configure project\",\n    \"Project:\",\n  );\n  if (projectSlug === null) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Run the command again to create a new project instead.\",\n    });\n  }\n  const { devDeployment } = await selectDevDeploymentType(ctx, {\n    chosenConfiguration,\n    newOrExisting: \"existing\",\n    teamSlug,\n    projectSlug,\n    userHasChosenSomethingInteractively: chosen || !config.project,\n    devDeploymentFromFlag: config.devDeployment,\n    forceDevDeployment: config.local\n      ? \"local\"\n      : config.cloud\n        ? \"cloud\"\n        : undefined,\n  });\n\n  showSpinner(ctx, `Reinitializing project ${projectSlug}...\\n`);\n\n  const { projectConfig: existingProjectConfig } = await readProjectConfig(ctx);\n\n  const functionsPath = functionsDir(configName(), existingProjectConfig);\n\n  await doCodegen(ctx, functionsPath, \"disable\");\n\n  logFinishedStep(ctx, `Reinitialized project ${chalk.bold(projectSlug)}`);\n  return { teamSlug, projectSlug, devDeployment };\n}\n\nasync function askToConfigure(ctx: Context): Promise<\"new\" | \"existing\"> {\n  if (!(await hasProjects(ctx))) {\n    return \"new\";\n  }\n  return await promptOptions(ctx, {\n    message: \"What would you like to configure?\",\n    default: \"new\",\n    choices: [\n      { name: \"create a new project\", value: \"new\" },\n      { name: \"choose an existing project\", value: \"existing\" },\n    ],\n  });\n}\n\ntype DeploymentOptions =\n  | {\n      kind: \"prod\";\n    }\n  | { kind: \"dev\" }\n  | {\n      kind: \"local\";\n      ports?: {\n        cloud: number;\n        site: number;\n      };\n      backendVersion?: string;\n      forceUpgrade: boolean;\n    };\n\n/**\n * This method assumes that the member has access to the selected project.\n */\nasync function ensureDeploymentProvisioned(\n  ctx: Context,\n  options: {\n    teamSlug: string;\n    projectSlug: string;\n    deploymentOptions: DeploymentOptions;\n    partitionId: number | undefined;\n  },\n): Promise<DeploymentDetails> {\n  switch (options.deploymentOptions.kind) {\n    case \"dev\":\n    case \"prod\": {\n      const credentials =\n        await fetchDeploymentCredentialsProvisioningDevOrProdMaybeThrows(\n          ctx,\n          {\n            kind: \"teamAndProjectSlugs\",\n            teamSlug: options.teamSlug,\n            projectSlug: options.projectSlug,\n          },\n          options.deploymentOptions.kind,\n          options.partitionId,\n        );\n      return {\n        ...credentials,\n        onActivity: null,\n      };\n    }\n    case \"local\": {\n      const credentials = await handleLocalDeployment(ctx, {\n        teamSlug: options.teamSlug,\n        projectSlug: options.projectSlug,\n        ...options.deploymentOptions,\n      });\n      return credentials;\n    }\n    default:\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Invalid deployment type: ${(options.deploymentOptions as any).kind}`,\n        errForSentry: `Invalid deployment type: ${(options.deploymentOptions as any).kind}`,\n      });\n  }\n}\n\nexport async function updateEnvAndConfigForDeploymentSelection(\n  ctx: Context,\n  options: {\n    url: string;\n    deploymentName: string;\n    teamSlug: string | null;\n    projectSlug: string | null;\n    deploymentType: DeploymentType;\n  },\n  existingValue: string | null,\n) {\n  const { configPath, projectConfig: existingProjectConfig } =\n    await readProjectConfig(ctx);\n\n  const functionsPath = functionsDir(configName(), existingProjectConfig);\n\n  const { wroteToGitIgnore, changedDeploymentEnvVar } =\n    await writeDeploymentEnvVar(\n      ctx,\n      options.deploymentType,\n      {\n        team: options.teamSlug,\n        project: options.projectSlug,\n        deploymentName: options.deploymentName,\n      },\n      existingValue,\n    );\n  const projectConfig = await upgradeOldAuthInfoToAuthConfig(\n    ctx,\n    existingProjectConfig,\n    functionsPath,\n  );\n  await writeProjectConfig(ctx, projectConfig, {\n    deleteIfAllDefault: true,\n  });\n  await finalizeConfiguration(ctx, {\n    deploymentType: options.deploymentType,\n    deploymentName: options.deploymentName,\n    url: options.url,\n    wroteToGitIgnore,\n    changedDeploymentEnvVar,\n    functionsPath: functionsDir(configPath, projectConfig),\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,qBAOO;AACP,iBASO;AACP,oBAKO;AACP,wBAIO;AACP,kBAAsC;AACtC,mBAQO;AACP,qBAAwC;AACxC,kBAAiB;AACjB,uBAAoC;AACpC,qBAAkD;AAClD,6BAAsC;AACtC,qBAIO;AACP,0BAAiC;AACjC,iCAKO;AACP,mBAA+B;AAC/B,uBAA0C;AAiD1C,eAAsB,iCACpB,KACA,qBACA,qBACA,YACA,aAUA;AACA,QAAM,qBAAqB,MAAM;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,MAAI,mBAAmB,qBAAqB,MAAM;AAEhD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,QACE,KAAK,mBAAmB;AAAA,QACxB,gBAAgB,mBAAmB,iBAAiB;AAAA,QACpD,UAAU,mBAAmB,iBAAiB;AAAA,QAC9C,aAAa,mBAAmB,iBAAiB;AAAA,QACjD,gBAAgB,mBAAmB,iBAAiB;AAAA,MACtD;AAAA,UACA,wDAA4B,mBAAmB;AAAA,IACjD;AAAA,EACF,OAAO;AAEL,UAAM,gCAAgC,KAAK;AAAA,MACzC,KAAK,mBAAmB;AAAA,MACxB,UAAU,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,KAAK,mBAAmB;AAAA,IACxB,UAAU,mBAAmB;AAAA,IAC7B,kBAAkB,mBAAmB;AAAA,EACvC;AACF;AAEA,eAAsB,kCACpB,KACA,qBACA,qBACA,YACA,aAUA;AACA,QAAM,aAAS,sCAAiB,GAAG;AACnC,QAAM,qBAAqB,CAAC,CAAC,QAAQ;AACrC,MAAI,sBAAsB,WAAW,OAAO;AAC1C,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AAEA,UAAQ,oBAAoB,MAAM;AAAA,IAChC,KAAK;AACH,gBAAM;AAAA,QACJ;AAAA,QACA,WAAW;AAAA,QACX,oBAAoB,kBAAkB;AAAA,MACxC;AACA,UAAI,oBAAoB,kBAAkB,qBAAqB,MAAM;AAEnE,cAAM,gCAAgC,KAAK;AAAA,UACzC,KAAK,oBAAoB,kBAAkB;AAAA,UAC3C,UAAU,oBAAoB,kBAAkB;AAAA,QAClD,CAAC;AAAA,MACH;AACA,aAAO;AAAA,QACL,KAAK,oBAAoB,kBAAkB;AAAA,QAC3C,UAAU,oBAAoB,kBAAkB;AAAA,QAChD,kBACE,oBAAoB,kBAAkB;AAAA,MAC1C;AAAA,IACF,KAAK,iBAAiB;AACpB,gBAAM,6BAAe,KAAK;AAAA,QACxB,iBAAiB,WAAW;AAAA,QAC5B,oBAAoB,WAAW;AAAA,QAC/B,sBAAsB,WAAW;AAAA,QACjC,sBAAsB,WAAW;AAAA,MACnC,CAAC;AACD,aAAO,MAAM;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,KAAK;AACH,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,KAAK,2BAA2B;AAC9B,aAAO,MAAM,8BAA8B,KAAK;AAAA,QAC9C;AAAA,QACA,eAAe,oBAAoB;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,KAAK,aAAa;AAChB,YAAM,UAAU,IAAI,aAAa,MAAM;AACvC,UAAI,WAAW,oBAAoB,mBAAmB,MAAM;AAC1D,cAAM,kBACJ,wBAAwB,QACvB,UAAM,4BAAY,KAAK;AAAA,UACtB,SAAS,GAAG,2CAA8B,kCAAkC,oBAAoB,cAAc;AAAA,QAChH,CAAC;AACH,YAAI,CAAC,iBAAiB;AACpB,iBAAO,MAAM,IAAI,MAAM;AAAA,YACrB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB;AAAA,UAClB,CAAC;AAAA,QACH;AACA,eAAO,MAAM;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,0CACJ,oBAAoB,mBAAmB,QACvC,wBAAwB;AAC1B,YAAM,uBAAuB,0CACzB,OACA,UAAM,8BAAc,KAAK;AAAA,QACvB,SACE;AAAA,QACF,SAAS;AAAA,UACP;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA,EAAE,MAAM,8BAA8B,OAAO,MAAM;AAAA,QACrD;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AACL,UAAI,yBAAyB,MAAM;AACjC,cAAM,SAAS,UAAM,4CAA0B,KAAK;AAAA,UAClD;AAAA,UACA,gBAAgB,oBAAoB;AAAA,UACpC,GAAG,WAAW;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL,UAAU,OAAO;AAAA,UACjB,KAAK,OAAO;AAAA,UACZ,kBAAkB;AAAA,YAChB,gBAAgB,OAAO;AAAA,YACvB,gBAAgB;AAAA,YAChB,aAAa;AAAA,YACb,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AACA,aAAO,MAAM;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,8BACb,KACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAOA;AACA,QAAM,UAAU,IAAI,aAAa,MAAM;AACvC,QAAM,eACJ,eAAW,4DAAgC,IACvC,SACA,2EAA2E,2CAA8B;AAC/G,YAAM,6BAAe,KAAK;AAAA,IACxB,SAAS;AAAA,IACT,iBAAiB,WAAW;AAAA,IAC5B,oBAAoB,WAAW;AAAA,IAC/B,sBAAsB,WAAW;AAAA,IACjC,sBAAsB,WAAW;AAAA,EACnC,CAAC;AACD,MAAI,wBAAwB,MAAM;AAChC,UAAM,SAAS,MAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,UAAM,yCAA6B,KAAK,aAAa;AAC1E,MAAI,aAAa,SAAS,YAAY;AACpC,mCAAW,KAAK,gDAAgD;AAChE,UAAM,SAAS,MAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,qBAAqB,UAAM;AAAA,IAC/B;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA,WAAW;AAAA;AAAA,IAEX,EAAE,oBAAoB,MAAM;AAAA,EAC9B;AACA,MACE,mBAAmB,qBAAqB,QACxC,mBAAmB,iBAAiB,mBAAmB,SACvD;AAEA,cAAM,8CAAsB,KAAK;AAAA,MAC/B,UAAU,mBAAmB,iBAAiB;AAAA,MAC9C,aAAa,mBAAmB,iBAAiB;AAAA,MACjD,cAAc,WAAW,aAAa;AAAA,MACtC,OAAO,WAAW,aAAa;AAAA,MAC/B,gBAAgB,WAAW,aAAa;AAAA,IAC1C,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,KAAK,mBAAmB;AAAA,IACxB,UAAU,mBAAmB;AAAA,IAC7B,kBAAkB,mBAAmB;AAAA,EACvC;AACF;AAEA,eAAe,oBACb,KACA,qBACA,MAIA,YAUA;AACA,YAAM,6BAAe,KAAK;AAAA,IACxB,iBAAiB,WAAW;AAAA,IAC5B,oBAAoB,WAAW;AAAA,IAC/B,sBAAsB,WAAW;AAAA,IACjC,sBAAsB,WAAW;AAAA,EACnC,CAAC;AACD,QAAM,UAAU,MAAM,cAAc,KAAK,qBAAqB;AAAA,IAC5D,MAAM,WAAW;AAAA,IACjB,SAAS,WAAW;AAAA,IACpB,eAAe,WAAW;AAAA,IAC1B,OAAO,KAAK,qBAAqB,QAAQ,WAAW;AAAA,IACpD,OAAO,KAAK,qBAAqB,OAAO,WAAW;AAAA,IACnD,aAAa,KAAK;AAAA,EACpB,CAAC;AAID,QAAM,oBACJ,WAAW,uBAAuB,SAAS,SACvC,EAAE,MAAM,OAAO,IACf,QAAQ,kBAAkB,UACxB,EAAE,MAAM,SAAS,GAAG,WAAW,aAAa,IAC5C,EAAE,MAAM,MAAM;AACtB,QAAM;AAAA,IACJ;AAAA,IACA,eAAe;AAAA,IACf;AAAA,EACF,IAAI,MAAM,4BAA4B,KAAK;AAAA,IACzC,UAAU,QAAQ;AAAA,IAClB,aAAa,QAAQ;AAAA,IACrB;AAAA,IACA,aAAa,KAAK;AAAA,EACpB,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,MACA,gBAAgB,kBAAkB;AAAA,MAClC,aAAa,QAAQ;AAAA,MACrB,UAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AACF;AAEA,eAAsB,gCACpB,KACA,YACA;AACA,QAAM,EAAE,KAAK,SAAS,IAAI;AAC1B,QAAM,WAAW,UAAM,yCAAsB,GAAG;AAChD,MAAI,UAAU;AACZ;AAAA,MACE;AAAA,MACA,aAAAA,QAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc,UAAM,wCAAwB,KAAK,GAAG;AAC1D,MAAI,gBAAgB,MAAM;AACxB;AAAA,MACE;AAAA,MACA,aAAAA,QAAM;AAAA,QACJ,4BAA4B,YAAY,MAAM,OAAO,YAAY,OAAO;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AACA,SAAO,EAAE,KAAK,SAAS;AACzB;AAEA,eAAsB,cACpB,KACA,qBACA,YAaC;AAED,QAAM,SACJ,wBAAwB,SAAS,wBAAwB,OACrD,sBACA,MAAM,eAAe,GAAG;AAC9B,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,iBAAiB,KAAK,qBAAqB,UAAU;AAAA,IAC9D,KAAK;AACH,aAAO,sBAAsB,KAAK,qBAAqB,UAAU;AAAA,IACnE;AACE,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,EACL;AACF;AAEA,MAAM,MAAM,YAAAC,QAAK,SAAS,QAAQ,IAAI,CAAC;AACvC,eAAe,iBACb,KACA,qBACA,QASA;AACA,QAAM,EAAE,UAAU,cAAc,QAAQ,sBAAsB,IAC5D,UAAM,mCAAqB,KAAK,OAAO,MAAM,OAAO;AACtD,MAAI,cAAsB,OAAO,WAAW;AAC5C,MAAI,4BAA4B;AAChC,MAAI,CAAC,OAAO,SAAS;AACnB,kBAAc,UAAM,6BAAa,KAAK;AAAA,MACpC,SAAS;AAAA,MACT,SAAS,OAAO,sBAAsB;AAAA,IACxC,CAAC;AACD,gCAA4B;AAAA,EAC9B;AAEA,QAAM,EAAE,cAAc,IAAI,UAAM,sCAAwB,KAAK;AAAA,IAC3D;AAAA,IACA,eAAe;AAAA,IACf,UAAU;AAAA,IACV,qCACE,yBAAyB;AAAA,IAC3B,aAAa;AAAA,IACb,uBAAuB,OAAO;AAAA,IAC9B,oBAAoB,OAAO,QACvB,UACA,OAAO,QACL,UACA;AAAA,EACR,CAAC;AAED,kCAAY,KAAK,gCAAgC;AAEjD,MAAI,aAAa,UAAU;AAC3B,MAAI;AACF,KAAC,EAAE,aAAa,UAAU,kBAAkB,IAAI,UAAM,0BAAc,KAAK;AAAA,MACvE,UAAU;AAAA,MACV;AAAA,MACA,aAAa,OAAO;AAAA;AAAA,MAEpB,2BAA2B,kBAAkB,UAAU,SAAS;AAAA,IAClE,CAAC;AAAA,EACH,SAAS,KAAK;AACZ,mCAAW,KAAK,2BAA2B;AAC3C,WAAO,UAAM,qCAAuB,KAAK,GAAG;AAAA,EAC9C;AACA,QAAM,cAAc,wBAChB,cAAc,aAAAD,QAAM,KAAK,QAAQ,IACjC;AACJ;AAAA,IACE;AAAA,IACA,mBAAmB,aAAAA,QAAM;AAAA,MACvB;AAAA,IACF,CAAC,GAAG,WAAW,kBAAkB,aAAAA,QAAM;AAAA,UACrC,sCAAoB,UAAU,WAAW;AAAA,IAC3C,CAAC;AAAA,EACH;AAEA,MAAI,qBAAqB,GAAG;AAC1B;AAAA,MACE;AAAA,MACA,aAAAA,QAAM,OAAO;AAAA,QACX,wBAAwB,iBAAiB,WACvC,sBAAsB,IAAI,KAAK,GACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,YAAM,uCAAuB,GAAG;AAChC,SAAO,EAAE,UAAU,aAAa,cAAc;AAChD;AAEA,eAAe,sBACb,KACA,qBACA,QAWC;AACD,QAAM,EAAE,UAAU,OAAO,IAAI,UAAM;AAAA,IACjC;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACF;AAEA,QAAM,cAAc,UAAM;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,gBAAgB,MAAM;AACxB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,EAAE,cAAc,IAAI,UAAM,sCAAwB,KAAK;AAAA,IAC3D;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA,qCAAqC,UAAU,CAAC,OAAO;AAAA,IACvD,uBAAuB,OAAO;AAAA,IAC9B,oBAAoB,OAAO,QACvB,UACA,OAAO,QACL,UACA;AAAA,EACR,CAAC;AAED,kCAAY,KAAK,0BAA0B,WAAW;AAAA,CAAO;AAE7D,QAAM,EAAE,eAAe,sBAAsB,IAAI,UAAM,iCAAkB,GAAG;AAE5E,QAAM,oBAAgB,+BAAa,0BAAW,GAAG,qBAAqB;AAEtE,YAAM,0BAAU,KAAK,eAAe,SAAS;AAE7C,sCAAgB,KAAK,yBAAyB,aAAAA,QAAM,KAAK,WAAW,CAAC,EAAE;AACvE,SAAO,EAAE,UAAU,aAAa,cAAc;AAChD;AAEA,eAAe,eAAe,KAA2C;AACvE,MAAI,CAAE,UAAM,0BAAY,GAAG,GAAI;AAC7B,WAAO;AAAA,EACT;AACA,SAAO,UAAM,8BAAc,KAAK;AAAA,IAC9B,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,MACP,EAAE,MAAM,wBAAwB,OAAO,MAAM;AAAA,MAC7C,EAAE,MAAM,8BAA8B,OAAO,WAAW;AAAA,IAC1D;AAAA,EACF,CAAC;AACH;AAoBA,eAAe,4BACb,KACA,SAM4B;AAC5B,UAAQ,QAAQ,kBAAkB,MAAM;AAAA,IACtC,KAAK;AAAA,IACL,KAAK,QAAQ;AACX,YAAM,cACJ,UAAM;AAAA,QACJ;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,UAAU,QAAQ;AAAA,UAClB,aAAa,QAAQ;AAAA,QACvB;AAAA,QACA,QAAQ,kBAAkB;AAAA,QAC1B,QAAQ;AAAA,MACV;AACF,aAAO;AAAA,QACL,GAAG;AAAA,QACH,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,KAAK,SAAS;AACZ,YAAM,cAAc,UAAM,8CAAsB,KAAK;AAAA,QACnD,UAAU,QAAQ;AAAA,QAClB,aAAa,QAAQ;AAAA,QACrB,GAAG,QAAQ;AAAA,MACb,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA;AACE,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,4BAA6B,QAAQ,kBAA0B,IAAI;AAAA,QACnF,cAAc,4BAA6B,QAAQ,kBAA0B,IAAI;AAAA,MACnF,CAAC;AAAA,EACL;AACF;AAEA,eAAsB,yCACpB,KACA,SAOA,eACA;AACA,QAAM,EAAE,YAAY,eAAe,sBAAsB,IACvD,UAAM,iCAAkB,GAAG;AAE7B,QAAM,oBAAgB,+BAAa,0BAAW,GAAG,qBAAqB;AAEtE,QAAM,EAAE,kBAAkB,wBAAwB,IAChD,UAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,MACE,MAAM,QAAQ;AAAA,MACd,SAAS,QAAQ;AAAA,MACjB,gBAAgB,QAAQ;AAAA,IAC1B;AAAA,IACA;AAAA,EACF;AACF,QAAM,gBAAgB,UAAM;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,YAAM,kCAAmB,KAAK,eAAe;AAAA,IAC3C,oBAAoB;AAAA,EACtB,CAAC;AACD,YAAM,mCAAsB,KAAK;AAAA,IAC/B,gBAAgB,QAAQ;AAAA,IACxB,gBAAgB,QAAQ;AAAA,IACxB,KAAK,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA,mBAAe,2BAAa,YAAY,aAAa;AAAA,EACvD,CAAC;AACH;", "names": ["chalk", "path"]}