{"version": 3, "sources": ["../../../src/cli/index.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { init } from \"./init.js\";\nimport { dashboard } from \"./dashboard.js\";\nimport { deployments } from \"./deployments.js\";\nimport { docs } from \"./docs.js\";\nimport { run } from \"./run.js\";\nimport { version } from \"./version.js\";\nimport { auth } from \"./auth.js\";\nimport { codegen } from \"./codegen.js\";\nimport { reinit } from \"./reinit.js\";\nimport { update } from \"./update.js\";\nimport { typecheck } from \"./typecheck.js\";\nimport { login } from \"./login.js\";\nimport { logout } from \"./logout.js\";\nimport chalk from \"chalk\";\nimport * as Sentry from \"@sentry/node\";\nimport { initSentry } from \"./lib/utils/sentry.js\";\nimport { dev } from \"./dev.js\";\nimport { deploy } from \"./deploy.js\";\nimport { logs } from \"./logs.js\";\nimport { networkTest } from \"./network_test.js\";\nimport { convexExport } from \"./convexExport.js\";\nimport { convexImport } from \"./convexImport.js\";\nimport { env } from \"./env.js\";\nimport { data } from \"./data.js\";\nimport inquirer from \"inquirer\";\nimport inquirerSearchList from \"inquirer-search-list\";\nimport { format } from \"util\";\nimport { functionSpec } from \"./functionSpec.js\";\nimport { disableLocalDeployments } from \"./disableLocalDev.js\";\nimport { mcp } from \"./mcp.js\";\nimport dns from \"node:dns\";\nimport net from \"node:net\";\n\nconst MINIMUM_MAJOR_VERSION = 16;\nconst MINIMUM_MINOR_VERSION = 15;\n\n// console.error before it started being red by default in Node.js v20\nfunction logToStderr(...args: unknown[]) {\n  process.stderr.write(`${format(...args)}\\n`);\n}\n\nasync function main() {\n  const nodeVersion = process.versions.node;\n  const majorVersion = parseInt(nodeVersion.split(\".\")[0], 10);\n  const minorVersion = parseInt(nodeVersion.split(\".\")[1], 10);\n\n  // Use ipv4 first for 127.0.0.1 in tests\n  dns.setDefaultResultOrder(\"ipv4first\");\n\n  // Increase the timeout from default 250ms for high latency situations,\n  // see https://github.com/nodejs/node/issues/54359.\n  if (majorVersion >= 20) {\n    // While we use Node.js v18 types\n    (net as any).setDefaultAutoSelectFamilyAttemptTimeout?.(1000);\n  }\n\n  initSentry();\n  inquirer.registerPrompt(\"search-list\", inquirerSearchList);\n\n  if (\n    majorVersion < MINIMUM_MAJOR_VERSION ||\n    (majorVersion === MINIMUM_MAJOR_VERSION &&\n      minorVersion < MINIMUM_MINOR_VERSION)\n  ) {\n    logToStderr(\n      chalk.red(\n        `Your Node version ${nodeVersion} is too old. Convex requires at least Node v${MINIMUM_MAJOR_VERSION}.${MINIMUM_MINOR_VERSION}`,\n      ),\n    );\n    logToStderr(\n      chalk.gray(\n        `You can use ${chalk.bold(\n          \"nvm\",\n        )} (https://github.com/nvm-sh/nvm#installing-and-updating) to manage different versions of Node.`,\n      ),\n    );\n    logToStderr(\n      chalk.gray(\n        \"After installing `nvm`, install the latest version of Node with \" +\n          chalk.bold(\"`nvm install node`.\"),\n      ),\n    );\n    logToStderr(\n      chalk.gray(\n        \"Then, activate the installed version in your terminal with \" +\n          chalk.bold(\"`nvm use`.\"),\n      ),\n    );\n    process.exit(1);\n  }\n\n  const program = new Command();\n  program\n    .name(\"convex\")\n    .usage(\"<command> [options]\")\n    .description(\"Start developing with Convex by running `npx convex dev`.\")\n    .addCommand(login, { hidden: true })\n    .addCommand(init, { hidden: true })\n    .addCommand(reinit, { hidden: true })\n    .addCommand(dev)\n    .addCommand(deploy)\n    .addCommand(deployments, { hidden: true })\n    .addCommand(run)\n    .addCommand(convexImport)\n    .addCommand(dashboard)\n    .addCommand(docs)\n    .addCommand(logs)\n    .addCommand(typecheck, { hidden: true })\n    .addCommand(auth, { hidden: true })\n    .addCommand(convexExport)\n    .addCommand(env)\n    .addCommand(data)\n    .addCommand(codegen)\n    .addCommand(update)\n    .addCommand(logout)\n    .addCommand(networkTest, { hidden: true })\n    .addCommand(functionSpec)\n    .addCommand(disableLocalDeployments)\n    .addCommand(mcp)\n    .addHelpCommand(\"help <command>\", \"Show help for given <command>\")\n    .version(version)\n    // Hide version and help so they don't clutter\n    // the list of commands.\n    .configureHelp({ visibleOptions: () => [] })\n    .showHelpAfterError();\n\n  // Run the command and be sure to flush Sentry before exiting.\n  try {\n    await program.parseAsync(process.argv);\n  } catch (e) {\n    Sentry.captureException(e);\n    process.exitCode = 1;\n    // This is too early to use `logError`, so just log directly.\n    // eslint-disable-next-line no-console\n    console.error(chalk.red(\"Unexpected Error: \" + e));\n  } finally {\n    await Sentry.close();\n  }\n  process.exit();\n}\nvoid main();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAAwB;AACxB,kBAAqB;AACrB,uBAA0B;AAC1B,yBAA4B;AAC5B,kBAAqB;AACrB,iBAAoB;AACpB,qBAAwB;AACxB,kBAAqB;AACrB,qBAAwB;AACxB,oBAAuB;AACvB,oBAAuB;AACvB,uBAA0B;AAC1B,mBAAsB;AACtB,oBAAuB;AACvB,mBAAkB;AAClB,aAAwB;AACxB,oBAA2B;AAC3B,iBAAoB;AACpB,oBAAuB;AACvB,kBAAqB;AACrB,0BAA4B;AAC5B,0BAA6B;AAC7B,0BAA6B;AAC7B,iBAAoB;AACpB,kBAAqB;AACrB,sBAAqB;AACrB,kCAA+B;AAC/B,kBAAuB;AACvB,0BAA6B;AAC7B,6BAAwC;AACxC,iBAAoB;AACpB,sBAAgB;AAChB,sBAAgB;AAEhB,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB;AAG9B,SAAS,eAAe,MAAiB;AACvC,UAAQ,OAAO,MAAM,OAAG,oBAAO,GAAG,IAAI,CAAC;AAAA,CAAI;AAC7C;AAEA,eAAe,OAAO;AACpB,QAAM,cAAc,QAAQ,SAAS;AACrC,QAAM,eAAe,SAAS,YAAY,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAC3D,QAAM,eAAe,SAAS,YAAY,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAG3D,kBAAAA,QAAI,sBAAsB,WAAW;AAIrC,MAAI,gBAAgB,IAAI;AAEtB,IAAC,gBAAAC,QAAY,2CAA2C,GAAI;AAAA,EAC9D;AAEA,gCAAW;AACX,kBAAAC,QAAS,eAAe,eAAe,4BAAAC,OAAkB;AAEzD,MACE,eAAe,yBACd,iBAAiB,yBAChB,eAAe,uBACjB;AACA;AAAA,MACE,aAAAC,QAAM;AAAA,QACJ,qBAAqB,WAAW,+CAA+C,qBAAqB,IAAI,qBAAqB;AAAA,MAC/H;AAAA,IACF;AACA;AAAA,MACE,aAAAA,QAAM;AAAA,QACJ,eAAe,aAAAA,QAAM;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA;AAAA,MACE,aAAAA,QAAM;AAAA,QACJ,qEACE,aAAAA,QAAM,KAAK,qBAAqB;AAAA,MACpC;AAAA,IACF;AACA;AAAA,MACE,aAAAA,QAAM;AAAA,QACJ,gEACE,aAAAA,QAAM,KAAK,YAAY;AAAA,MAC3B;AAAA,IACF;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,QAAM,UAAU,IAAI,6BAAQ;AAC5B,UACG,KAAK,QAAQ,EACb,MAAM,qBAAqB,EAC3B,YAAY,2DAA2D,EACvE,WAAW,oBAAO,EAAE,QAAQ,KAAK,CAAC,EAClC,WAAW,kBAAM,EAAE,QAAQ,KAAK,CAAC,EACjC,WAAW,sBAAQ,EAAE,QAAQ,KAAK,CAAC,EACnC,WAAW,cAAG,EACd,WAAW,oBAAM,EACjB,WAAW,gCAAa,EAAE,QAAQ,KAAK,CAAC,EACxC,WAAW,cAAG,EACd,WAAW,gCAAY,EACvB,WAAW,0BAAS,EACpB,WAAW,gBAAI,EACf,WAAW,gBAAI,EACf,WAAW,4BAAW,EAAE,QAAQ,KAAK,CAAC,EACtC,WAAW,kBAAM,EAAE,QAAQ,KAAK,CAAC,EACjC,WAAW,gCAAY,EACvB,WAAW,cAAG,EACd,WAAW,gBAAI,EACf,WAAW,sBAAO,EAClB,WAAW,oBAAM,EACjB,WAAW,oBAAM,EACjB,WAAW,iCAAa,EAAE,QAAQ,KAAK,CAAC,EACxC,WAAW,gCAAY,EACvB,WAAW,8CAAuB,EAClC,WAAW,cAAG,EACd,eAAe,kBAAkB,+BAA+B,EAChE,QAAQ,sBAAO,EAGf,cAAc,EAAE,gBAAgB,MAAM,CAAC,EAAE,CAAC,EAC1C,mBAAmB;AAGtB,MAAI;AACF,UAAM,QAAQ,WAAW,QAAQ,IAAI;AAAA,EACvC,SAAS,GAAG;AACV,WAAO,iBAAiB,CAAC;AACzB,YAAQ,WAAW;AAGnB,YAAQ,MAAM,aAAAA,QAAM,IAAI,uBAAuB,CAAC,CAAC;AAAA,EACnD,UAAE;AACA,UAAM,OAAO,MAAM;AAAA,EACrB;AACA,UAAQ,KAAK;AACf;AACA,KAAK,KAAK;", "names": ["dns", "net", "inquirer", "inquirerSearchList", "chalk"]}