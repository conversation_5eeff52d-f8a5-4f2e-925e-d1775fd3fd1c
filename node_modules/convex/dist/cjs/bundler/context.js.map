{"version": 3, "sources": ["../../../src/bundler/context.ts"], "sourcesContent": ["import * as Sentry from \"@sentry/node\";\nimport chalk from \"chalk\";\nimport ora, { Ora } from \"ora\";\nimport { Filesystem, nodeFs } from \"./fs.js\";\nimport { format } from \"util\";\nimport ProgressBar from \"progress\";\nimport { initializeBigBrainAuth } from \"../cli/lib/deploymentSelection.js\";\n// How the error should be handled when running `npx convex dev`.\nexport type ErrorType =\n  // The error was likely caused by the state of the developer's local\n  // file system (e.g. `tsc` fails due to a syntax error). The `convex dev`\n  // command will then print out the error and wait for the file to change before\n  // retrying.\n  | \"invalid filesystem data\"\n  // The error was caused by either the local state (ie schema.ts content)\n  // or the state of the db (ie documents not matching the new schema).\n  // The `convex dev` command will wait for either file OR table data change\n  // to retry (if a table name is specified as the value in this Object).\n  | {\n      \"invalid filesystem or db data\": {\n        tableName: string;\n        componentPath?: string;\n      } | null;\n    }\n  // The error was caused by either the local state (ie schema.ts content)\n  // or the state of the deployment environment variables.\n  // The `convex dev` command will wait for either file OR env var change\n  // before retrying.\n  | \"invalid filesystem or env vars\"\n  // The error was some transient issue (e.g. a network\n  // error). This will then cause a retry after an exponential backoff.\n  | \"transient\"\n  // This error is truly permanent. Exit `npx convex dev` because the\n  // developer will need to take a manual commandline action.\n  | \"fatal\";\n\nexport type BigBrainAuth = {\n  header: string;\n} & (\n  | {\n      kind: \"projectKey\";\n      projectKey: string;\n    }\n  | {\n      kind: \"previewDeployKey\";\n      previewDeployKey: string;\n    }\n  | {\n      kind: \"accessToken\";\n      accessToken: string;\n    }\n);\n\nexport interface Context {\n  fs: Filesystem;\n  deprecationMessagePrinted: boolean;\n  spinner: Ora | undefined;\n  // Reports to Sentry and either throws FatalError or exits the process.\n  // Prints the `printedMessage` if provided\n  crash(args: {\n    exitCode: number;\n    errorType: ErrorType;\n    errForSentry?: any;\n    printedMessage: string | null;\n  }): Promise<never>;\n  registerCleanup(fn: (exitCode: number, err?: any) => Promise<void>): string;\n  removeCleanup(\n    handle: string,\n  ): (exitCode: number, err?: any) => Promise<void> | null;\n  bigBrainAuth(): BigBrainAuth | null;\n  /**\n   * Prefer using `updateBigBrainAuthAfterLogin` in `deploymentSelection.ts` instead\n   */\n  _updateBigBrainAuth(auth: BigBrainAuth | null): void;\n}\n\nasync function flushAndExit(exitCode: number, err?: any) {\n  if (err) {\n    Sentry.captureException(err);\n  }\n  await Sentry.close();\n  return process.exit(exitCode);\n}\n\nexport type OneoffCtx = Context & {\n  // Generally `ctx.crash` is better to use since it handles printing a message\n  // for the user, and then calls this.\n  //\n  // This function reports to Sentry + exits the process, but does not handle\n  // printing a message for the user.\n  flushAndExit: (exitCode: number, err?: any) => Promise<never>;\n};\n\nclass OneoffContextImpl {\n  private _cleanupFns: Record<\n    string,\n    (exitCode: number, err?: any) => Promise<void>\n  > = {};\n  public fs: Filesystem = nodeFs;\n  public deprecationMessagePrinted: boolean = false;\n  public spinner: Ora | undefined = undefined;\n  private _bigBrainAuth: BigBrainAuth | null = null;\n\n  crash = async (args: {\n    exitCode: number;\n    errorType?: ErrorType;\n    errForSentry?: any;\n    printedMessage: string | null;\n  }) => {\n    if (args.printedMessage !== null) {\n      logFailure(this, args.printedMessage);\n    }\n    return await this.flushAndExit(args.exitCode, args.errForSentry);\n  };\n  flushAndExit = async (exitCode: number, err?: any) => {\n    logVerbose(this, \"Flushing and exiting, error:\", err);\n    if (err) {\n      logVerbose(this, err.stack);\n    }\n    const cleanupFns = this._cleanupFns;\n    // Clear the cleanup functions so that there's no risk of running them twice\n    // if this somehow gets triggered twice.\n    this._cleanupFns = {};\n    const fns = Object.values(cleanupFns);\n    logVerbose(this, `Running ${fns.length} cleanup functions`);\n    for (const fn of fns) {\n      await fn(exitCode, err);\n    }\n    logVerbose(this, \"All cleanup functions ran\");\n    return flushAndExit(exitCode, err);\n  };\n  registerCleanup(fn: (exitCode: number, err?: any) => Promise<void>) {\n    const handle = Math.random().toString(36).slice(2);\n    this._cleanupFns[handle] = fn;\n    return handle;\n  }\n  removeCleanup(handle: string) {\n    const value = this._cleanupFns[handle];\n    delete this._cleanupFns[handle];\n    return value ?? null;\n  }\n  bigBrainAuth(): BigBrainAuth | null {\n    return this._bigBrainAuth;\n  }\n  _updateBigBrainAuth(auth: BigBrainAuth | null): void {\n    logVerbose(this, `Updating big brain auth to ${auth?.kind ?? \"null\"}`);\n    this._bigBrainAuth = auth;\n  }\n}\n\nexport const oneoffContext: (args: {\n  url?: string;\n  adminKey?: string;\n  envFile?: string;\n}) => Promise<OneoffCtx> = async (args) => {\n  const ctx = new OneoffContextImpl();\n  await initializeBigBrainAuth(ctx, {\n    url: args.url,\n    adminKey: args.adminKey,\n    envFile: args.envFile,\n  });\n  return ctx;\n};\n// console.error before it started being red by default in Node v20\nfunction logToStderr(...args: unknown[]) {\n  process.stderr.write(`${format(...args)}\\n`);\n}\n\n// Handles clearing spinner so that it doesn't get messed up\nexport function logError(ctx: Context, message: string) {\n  ctx.spinner?.clear();\n  logToStderr(message);\n}\n\n// Handles clearing spinner so that it doesn't get messed up\nexport function logWarning(ctx: Context, ...logged: any) {\n  ctx.spinner?.clear();\n  logToStderr(...logged);\n}\n\n// Handles clearing spinner so that it doesn't get messed up\nexport function logMessage(ctx: Context, ...logged: any) {\n  ctx.spinner?.clear();\n  logToStderr(...logged);\n}\n\n// For the rare case writing output to stdout. Status and error messages\n// (logMessage, logWarning, etc.) should be written to stderr.\nexport function logOutput(ctx: Context, ...logged: any) {\n  ctx.spinner?.clear();\n  // the one spot where we can console.log\n  // eslint-disable-next-line no-console\n  console.log(...logged);\n}\n\nexport function logVerbose(ctx: Context, ...logged: any) {\n  if (process.env.CONVEX_VERBOSE) {\n    logMessage(ctx, `[verbose] ${new Date().toISOString()}`, ...logged);\n  }\n}\n\n/**\n * Returns a ProgressBar instance, and also handles clearing the spinner if necessary.\n *\n * The caller is responsible for calling `progressBar.tick()` and terminating the `progressBar`\n * when it's done.\n */\nexport function startLogProgress(\n  ctx: Context,\n  format: string,\n  progressBarOptions: ProgressBar.ProgressBarOptions,\n): ProgressBar {\n  ctx.spinner?.clear();\n  return new ProgressBar(format, progressBarOptions);\n}\n\n// Start a spinner.\n// To change its message use changeSpinner.\n// To print warnings/errors while it's running use logError or logWarning.\n// To stop it due to an error use logFailure.\n// To stop it due to success use logFinishedStep.\nexport function showSpinner(ctx: Context, message: string) {\n  ctx.spinner?.stop();\n  ctx.spinner = ora({\n    // Add newline to prevent clobbering when a message\n    // we can't pipe through `logMessage` et al gets printed\n    text: message + \"\\n\",\n    stream: process.stderr,\n    // hideCursor: true doesn't work with `tsx`.\n    // see https://github.com/tapjs/signal-exit/issues/49#issuecomment-1459408082\n    // See CX-6822 for an issue to bring back cursor hiding, probably by upgrading libraries.\n    hideCursor: process.env.CONVEX_RUNNING_LIVE_IN_MONOREPO ? false : true,\n  }).start();\n}\n\nexport function changeSpinner(ctx: Context, message: string) {\n  if (ctx.spinner) {\n    // Add newline to prevent clobbering\n    ctx.spinner.text = message + \"\\n\";\n  } else {\n    logToStderr(message);\n  }\n}\n\nexport function logFailure(ctx: Context, message: string) {\n  if (ctx.spinner) {\n    ctx.spinner.fail(message);\n    ctx.spinner = undefined;\n  } else {\n    logToStderr(`${chalk.red(`✖`)} ${message}`);\n  }\n}\n\n// Stops and removes spinner if one is active\nexport function logFinishedStep(ctx: Context, message: string) {\n  if (ctx.spinner) {\n    ctx.spinner.succeed(message);\n    ctx.spinner = undefined;\n  } else {\n    logToStderr(`${chalk.green(`✔`)} ${message}`);\n  }\n}\n\nexport function stopSpinner(ctx: Context) {\n  if (ctx.spinner) {\n    ctx.spinner.stop();\n    ctx.spinner = undefined;\n  }\n}\n\n// Only shows the spinner if the async `fn` takes longer than `delayMs`\nexport async function showSpinnerIfSlow(\n  ctx: Context,\n  message: string,\n  delayMs: number,\n  fn: () => Promise<any>,\n) {\n  const timeout = setTimeout(() => {\n    showSpinner(ctx, message);\n  }, delayMs);\n  await fn();\n  clearTimeout(timeout);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAwB;AACxB,mBAAkB;AAClB,iBAAyB;AACzB,gBAAmC;AACnC,kBAAuB;AACvB,sBAAwB;AACxB,iCAAuC;AAsEvC,eAAe,aAAa,UAAkB,KAAW;AACvD,MAAI,KAAK;AACP,WAAO,iBAAiB,GAAG;AAAA,EAC7B;AACA,QAAM,OAAO,MAAM;AACnB,SAAO,QAAQ,KAAK,QAAQ;AAC9B;AAWA,MAAM,kBAAkB;AAAA,EAAxB;AACE,wBAAQ,eAGJ,CAAC;AACL,wBAAO,MAAiB;AACxB,wBAAO,6BAAqC;AAC5C,wBAAO;AACP,wBAAQ,iBAAqC;AAE7C,iCAAQ,OAAO,SAKT;AACJ,UAAI,KAAK,mBAAmB,MAAM;AAChC,mBAAW,MAAM,KAAK,cAAc;AAAA,MACtC;AACA,aAAO,MAAM,KAAK,aAAa,KAAK,UAAU,KAAK,YAAY;AAAA,IACjE;AACA,wCAAe,OAAO,UAAkB,QAAc;AACpD,iBAAW,MAAM,gCAAgC,GAAG;AACpD,UAAI,KAAK;AACP,mBAAW,MAAM,IAAI,KAAK;AAAA,MAC5B;AACA,YAAM,aAAa,KAAK;AAGxB,WAAK,cAAc,CAAC;AACpB,YAAM,MAAM,OAAO,OAAO,UAAU;AACpC,iBAAW,MAAM,WAAW,IAAI,MAAM,oBAAoB;AAC1D,iBAAW,MAAM,KAAK;AACpB,cAAM,GAAG,UAAU,GAAG;AAAA,MACxB;AACA,iBAAW,MAAM,2BAA2B;AAC5C,aAAO,aAAa,UAAU,GAAG;AAAA,IACnC;AAAA;AAAA,EACA,gBAAgB,IAAoD;AAClE,UAAM,SAAS,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AACjD,SAAK,YAAY,MAAM,IAAI;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAgB;AAC5B,UAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,WAAO,KAAK,YAAY,MAAM;AAC9B,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,eAAoC;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB,MAAiC;AACnD,eAAW,MAAM,8BAA8B,MAAM,QAAQ,MAAM,EAAE;AACrE,SAAK,gBAAgB;AAAA,EACvB;AACF;AAEO,MAAM,gBAIc,OAAO,SAAS;AACzC,QAAM,MAAM,IAAI,kBAAkB;AAClC,YAAM,mDAAuB,KAAK;AAAA,IAChC,KAAK,KAAK;AAAA,IACV,UAAU,KAAK;AAAA,IACf,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,MAAiB;AACvC,UAAQ,OAAO,MAAM,OAAG,oBAAO,GAAG,IAAI,CAAC;AAAA,CAAI;AAC7C;AAGO,SAAS,SAAS,KAAc,SAAiB;AACtD,MAAI,SAAS,MAAM;AACnB,cAAY,OAAO;AACrB;AAGO,SAAS,WAAW,QAAiB,QAAa;AACvD,MAAI,SAAS,MAAM;AACnB,cAAY,GAAG,MAAM;AACvB;AAGO,SAAS,WAAW,QAAiB,QAAa;AACvD,MAAI,SAAS,MAAM;AACnB,cAAY,GAAG,MAAM;AACvB;AAIO,SAAS,UAAU,QAAiB,QAAa;AACtD,MAAI,SAAS,MAAM;AAGnB,UAAQ,IAAI,GAAG,MAAM;AACvB;AAEO,SAAS,WAAW,QAAiB,QAAa;AACvD,MAAI,QAAQ,IAAI,gBAAgB;AAC9B,eAAW,KAAK,cAAa,oBAAI,KAAK,GAAE,YAAY,CAAC,IAAI,GAAG,MAAM;AAAA,EACpE;AACF;AAQO,SAAS,iBACd,KACAA,SACA,oBACa;AACb,MAAI,SAAS,MAAM;AACnB,SAAO,IAAI,gBAAAC,QAAYD,SAAQ,kBAAkB;AACnD;AAOO,SAAS,YAAY,KAAc,SAAiB;AACzD,MAAI,SAAS,KAAK;AAClB,MAAI,cAAU,WAAAE,SAAI;AAAA;AAAA;AAAA,IAGhB,MAAM,UAAU;AAAA,IAChB,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA,IAIhB,YAAY,QAAQ,IAAI,kCAAkC,QAAQ;AAAA,EACpE,CAAC,EAAE,MAAM;AACX;AAEO,SAAS,cAAc,KAAc,SAAiB;AAC3D,MAAI,IAAI,SAAS;AAEf,QAAI,QAAQ,OAAO,UAAU;AAAA,EAC/B,OAAO;AACL,gBAAY,OAAO;AAAA,EACrB;AACF;AAEO,SAAS,WAAW,KAAc,SAAiB;AACxD,MAAI,IAAI,SAAS;AACf,QAAI,QAAQ,KAAK,OAAO;AACxB,QAAI,UAAU;AAAA,EAChB,OAAO;AACL,gBAAY,GAAG,aAAAC,QAAM,IAAI,QAAG,CAAC,IAAI,OAAO,EAAE;AAAA,EAC5C;AACF;AAGO,SAAS,gBAAgB,KAAc,SAAiB;AAC7D,MAAI,IAAI,SAAS;AACf,QAAI,QAAQ,QAAQ,OAAO;AAC3B,QAAI,UAAU;AAAA,EAChB,OAAO;AACL,gBAAY,GAAG,aAAAA,QAAM,MAAM,QAAG,CAAC,IAAI,OAAO,EAAE;AAAA,EAC9C;AACF;AAEO,SAAS,YAAY,KAAc;AACxC,MAAI,IAAI,SAAS;AACf,QAAI,QAAQ,KAAK;AACjB,QAAI,UAAU;AAAA,EAChB;AACF;AAGA,eAAsB,kBACpB,KACA,SACA,SACA,IACA;AACA,QAAM,UAAU,WAAW,MAAM;AAC/B,gBAAY,KAAK,OAAO;AAAA,EAC1B,GAAG,OAAO;AACV,QAAM,GAAG;AACT,eAAa,OAAO;AACtB;", "names": ["format", "ProgressBar", "ora", "chalk"]}