{"version": 3, "sources": ["../../../src/values/validators.ts"], "sourcesContent": ["import { GenericId } from \"./index.js\";\nimport { GenericValidator } from \"./validator.js\";\nimport { JSONValue, convexToJson } from \"./value.js\";\n\ntype TableNameFromType<T> =\n  T extends GenericId<infer TableName> ? TableName : string;\n\n/**\n * Avoid using `instanceof BaseValidator`; this is inheritence for code reuse\n * not type heirarchy.\n */\nabstract class BaseValidator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> {\n  /**\n   * Only for TypeScript, the TS type of the JS values validated\n   * by this validator.\n   */\n  readonly type!: Type;\n  /**\n   * Only for TypeScript, if this an Object validator, then\n   * this is the TS type of its property names.\n   */\n  readonly fieldPaths!: FieldPaths;\n\n  /**\n   * Whether this is an optional Object property value validator.\n   */\n  readonly isOptional: IsOptional;\n\n  /**\n   * Always `\"true\"`.\n   */\n  readonly isConvexValidator: true;\n\n  constructor({ isOptional }: { isOptional: IsOptional }) {\n    this.isOptional = isOptional;\n    this.isConvexValidator = true;\n  }\n  /** @deprecated - use isOptional instead */\n  get optional(): boolean {\n    return this.isOptional === \"optional\" ? true : false;\n  }\n  /** @internal */\n  abstract get json(): ValidatorJSON;\n  /** @internal */\n  abstract asOptional(): Validator<Type | undefined, \"optional\", FieldPaths>;\n}\n\n/**\n * The type of the `v.id(tableName)` validator.\n */\nexport class VId<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The name of the table that the validated IDs must belong to.\n   */\n  readonly tableName: TableNameFromType<Type>;\n\n  /**\n   * The kind of validator, `\"id\"`.\n   */\n  readonly kind = \"id\" as const;\n\n  /**\n   * Usually you'd use `v.id(tableName)` instead.\n   */\n  constructor({\n    isOptional,\n    tableName,\n  }: {\n    isOptional: IsOptional;\n    tableName: TableNameFromType<Type>;\n  }) {\n    super({ isOptional });\n    this.tableName = tableName;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: \"id\", tableName: this.tableName };\n  }\n  /** @internal */\n  asOptional() {\n    return new VId<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      tableName: this.tableName,\n    });\n  }\n}\n\n/**\n * The type of the `v.float64()` validator.\n */\nexport class VFloat64<\n  Type = number,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"float64\"`.\n   */\n  readonly kind = \"float64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `number` string instead of `float64`.\n    return { type: \"number\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VFloat64<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.int64()` validator.\n */\nexport class VInt64<\n  Type = bigint,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"int64\"`.\n   */\n  readonly kind = \"int64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `bigint`.\n    return { type: \"bigint\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VInt64<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.boolean()` validator.\n */\nexport class VBoolean<\n  Type = boolean,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"boolean\"`.\n   */\n  readonly kind = \"boolean\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBoolean<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.bytes()` validator.\n */\nexport class VBytes<\n  Type = ArrayBuffer,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"bytes\"`.\n   */\n  readonly kind = \"bytes\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBytes<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.string()` validator.\n */\nexport class VString<\n  Type = string,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"string\"`.\n   */\n  readonly kind = \"string\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VString<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.null()` validator.\n */\nexport class VNull<\n  Type = null,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"null\"`.\n   */\n  readonly kind = \"null\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VNull<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.any()` validator.\n */\nexport class VAny<\n  Type = any,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The kind of validator, `\"any\"`.\n   */\n  readonly kind = \"any\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VAny<Type | undefined, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.object()` validator.\n */\nexport class VObject<\n  Type,\n  Fields extends Record<string, GenericValidator>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = {\n    [Property in keyof Fields]:\n      | JoinFieldPaths<Property & string, Fields[Property][\"fieldPaths\"]>\n      | Property;\n  }[keyof Fields] &\n    string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * An object with the validator for each property.\n   */\n  readonly fields: Fields;\n\n  /**\n   * The kind of validator, `\"object\"`.\n   */\n  readonly kind = \"object\" as const;\n\n  /**\n   * Usually you'd use `v.object({ ... })` instead.\n   */\n  constructor({\n    isOptional,\n    fields,\n  }: {\n    isOptional: IsOptional;\n    fields: Fields;\n  }) {\n    super({ isOptional });\n    this.fields = fields;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: globalThis.Object.fromEntries(\n        globalThis.Object.entries(this.fields).map(([k, v]) => [\n          k,\n          {\n            fieldType: v.json,\n            optional: v.isOptional === \"optional\" ? true : false,\n          },\n        ]),\n      ),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VObject<Type | undefined, Fields, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      fields: this.fields,\n    });\n  }\n}\n\n/**\n * The type of the `v.literal()` validator.\n */\nexport class VLiteral<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The value that the validated values must be equal to.\n   */\n  readonly value: Type;\n\n  /**\n   * The kind of validator, `\"literal\"`.\n   */\n  readonly kind = \"literal\" as const;\n\n  /**\n   * Usually you'd use `v.literal(value)` instead.\n   */\n  constructor({ isOptional, value }: { isOptional: IsOptional; value: Type }) {\n    super({ isOptional });\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: convexToJson(this.value as string | boolean | number | bigint),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VLiteral<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.array()` validator.\n */\nexport class VArray<\n  Type,\n  Element extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The validator for the elements of the array.\n   */\n  readonly element: Element;\n\n  /**\n   * The kind of validator, `\"array\"`.\n   */\n  readonly kind = \"array\" as const;\n\n  /**\n   * Usually you'd use `v.array(element)` instead.\n   */\n  constructor({\n    isOptional,\n    element,\n  }: {\n    isOptional: IsOptional;\n    element: Element;\n  }) {\n    super({ isOptional });\n    this.element = element;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.element.json,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VArray<Type | undefined, Element, \"optional\">({\n      isOptional: \"optional\",\n      element: this.element,\n    });\n  }\n}\n\n/**\n * The type of the `v.record()` validator.\n */\nexport class VRecord<\n  Type,\n  Key extends Validator<string, \"required\", any>,\n  Value extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The validator for the keys of the record.\n   */\n  readonly key: Key;\n\n  /**\n   * The validator for the values of the record.\n   */\n  readonly value: Value;\n\n  /**\n   * The kind of validator, `\"record\"`.\n   */\n  readonly kind = \"record\" as const;\n\n  /**\n   * Usually you'd use `v.record(key, value)` instead.\n   */\n  constructor({\n    isOptional,\n    key,\n    value,\n  }: {\n    isOptional: IsOptional;\n    key: Key;\n    value: Value;\n  }) {\n    super({ isOptional });\n    if ((key.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional keys\");\n    }\n    if ((value.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional values\");\n    }\n    this.key = key;\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      // This cast is needed because TypeScript thinks the key type is too wide\n      keys: this.key.json as RecordKeyValidatorJSON,\n      values: {\n        fieldType: this.value.json,\n        optional: false,\n      },\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      key: this.key,\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.union()` validator.\n */\nexport class VUnion<\n  Type,\n  T extends Validator<any, \"required\", any>[],\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = T[number][\"fieldPaths\"],\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The array of validators, one of which must match the value.\n   */\n  readonly members: T;\n\n  /**\n   * The kind of validator, `\"union\"`.\n   */\n  readonly kind = \"union\" as const;\n\n  /**\n   * Usually you'd use `v.union(...members)` instead.\n   */\n  constructor({ isOptional, members }: { isOptional: IsOptional; members: T }) {\n    super({ isOptional });\n    this.members = members;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.members.map((v) => v.json),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VUnion<Type | undefined, T, \"optional\">({\n      isOptional: \"optional\",\n      members: this.members,\n    });\n  }\n}\n\n// prettier-ignore\nexport type VOptional<T extends Validator<any, OptionalProperty, any>> =\n  T extends VId<infer Type, OptionalProperty> ? VId<Type | undefined, \"optional\">\n  : T extends VString<infer Type, OptionalProperty>\n    ? VString<Type | undefined, \"optional\">\n  : T extends VFloat64<infer Type, OptionalProperty>\n    ? VFloat64<Type | undefined, \"optional\">\n  : T extends VInt64<infer Type, OptionalProperty>\n    ? VInt64<Type | undefined, \"optional\">\n  : T extends VBoolean<infer Type, OptionalProperty>\n    ? VBoolean<Type | undefined, \"optional\">\n  : T extends VNull<infer Type, OptionalProperty>\n    ? VNull<Type | undefined, \"optional\">\n  : T extends VAny<infer Type, OptionalProperty>\n    ? VAny<Type | undefined, \"optional\">\n  : T extends VLiteral<infer Type, OptionalProperty>\n    ? VLiteral<Type | undefined, \"optional\">\n  : T extends VBytes<infer Type, OptionalProperty>\n    ? VBytes<Type | undefined, \"optional\">\n  : T extends VObject< infer Type, infer Fields, OptionalProperty, infer FieldPaths>\n    ? VObject<Type | undefined, Fields, \"optional\", FieldPaths>\n  : T extends VArray<infer Type, infer Element, OptionalProperty>\n    ? VArray<Type | undefined, Element, \"optional\">\n  : T extends VRecord< infer Type, infer Key, infer Value, OptionalProperty, infer FieldPaths>\n    ? VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>\n  : T extends VUnion<infer Type, infer Members, OptionalProperty, infer FieldPaths>\n    ? VUnion<Type | undefined, Members, \"optional\", FieldPaths>\n  : never\n\n/**\n * Type representing whether a property in an object is optional or required.\n *\n * @public\n */\nexport type OptionalProperty = \"optional\" | \"required\";\n\n/**\n * A validator for a Convex value.\n *\n * This should be constructed using the validator builder, {@link v}.\n *\n * A validator encapsulates:\n * - The TypeScript type of this value.\n * - Whether this field should be optional if it's included in an object.\n * - The TypeScript type for the set of index field paths that can be used to\n * build indexes on this value.\n * - A JSON representation of the validator.\n *\n * Specific types of validators contain additional information: for example\n * an `ArrayValidator` contains an `element` property with the validator\n * used to validate each element of the list. Use the shared 'kind' property\n * to identity the type of validator.\n *\n * More validators can be added in future releases so an exhaustive\n * switch statement on validator `kind` should be expected to break\n * in future releases of Convex.\n *\n * @public\n */\nexport type Validator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> =\n  | VId<Type, IsOptional>\n  | VString<Type, IsOptional>\n  | VFloat64<Type, IsOptional>\n  | VInt64<Type, IsOptional>\n  | VBoolean<Type, IsOptional>\n  | VNull<Type, IsOptional>\n  | VAny<Type, IsOptional>\n  | VLiteral<Type, IsOptional>\n  | VBytes<Type, IsOptional>\n  | VObject<\n      Type,\n      Record<string, Validator<any, OptionalProperty, any>>,\n      IsOptional,\n      FieldPaths\n    >\n  | VArray<Type, Validator<any, \"required\", any>, IsOptional>\n  | VRecord<\n      Type,\n      Validator<string, \"required\", any>,\n      Validator<any, \"required\", any>,\n      IsOptional,\n      FieldPaths\n    >\n  | VUnion<Type, Validator<any, \"required\", any>[], IsOptional, FieldPaths>;\n\n/**\n * Join together two index field paths.\n *\n * This is used within the validator builder, {@link v}.\n * @public\n */\nexport type JoinFieldPaths<\n  Start extends string,\n  End extends string,\n> = `${Start}.${End}`;\n\nexport type ObjectFieldType = { fieldType: ValidatorJSON; optional: boolean };\n\nexport type ValidatorJSON =\n  | { type: \"null\" }\n  | { type: \"number\" }\n  | { type: \"bigint\" }\n  | { type: \"boolean\" }\n  | { type: \"string\" }\n  | { type: \"bytes\" }\n  | { type: \"any\" }\n  | { type: \"literal\"; value: JSONValue }\n  | { type: \"id\"; tableName: string }\n  | { type: \"array\"; value: ValidatorJSON }\n  | {\n      type: \"record\";\n      keys: RecordKeyValidatorJSON;\n      values: RecordValueValidatorJSON;\n    }\n  | { type: \"object\"; value: Record<string, ObjectFieldType> }\n  | { type: \"union\"; value: ValidatorJSON[] };\n\nexport type RecordKeyValidatorJSON =\n  | { type: \"string\" }\n  | { type: \"id\"; tableName: string }\n  | { type: \"union\"; value: RecordKeyValidatorJSON[] };\n\nexport type RecordValueValidatorJSON = ObjectFieldType & { optional: false };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,mBAAwC;AASxC,MAAe,cAIb;AAAA,EAsBA,YAAY,EAAE,WAAW,GAA+B;AAjBxD;AAAA;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS;AAGP,SAAK,aAAa;AAClB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,WAAoB;AACtB,WAAO,KAAK,eAAe,aAAa,OAAO;AAAA,EACjD;AAKF;AAKO,MAAM,YAGH,cAAgC;AAAA;AAAA;AAAA;AAAA,EAcxC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAGG;AACD,UAAM,EAAE,WAAW,CAAC;AAjBtB;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS,QAAO;AAad,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,MAAM,WAAW,KAAK,UAAU;AAAA,EACjD;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,IAAkC;AAAA,MAC3C,YAAY;AAAA,MACZ,WAAW,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AACF;AAKO,MAAM,iBAGH,cAAgC;AAAA,EAHnC;AAAA;AAOL;AAAA;AAAA;AAAA,wBAAS,QAAO;AAAA;AAAA;AAAA,EAGhB,IAAI,OAAsB;AAExB,WAAO,EAAE,MAAM,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,SAAuC;AAAA,MAChD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAKO,MAAM,eAGH,cAAgC;AAAA,EAHnC;AAAA;AAOL;AAAA;AAAA;AAAA,wBAAS,QAAO;AAAA;AAAA;AAAA,EAGhB,IAAI,OAAsB;AAExB,WAAO,EAAE,MAAM,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,OAAqC,EAAE,YAAY,WAAW,CAAC;AAAA,EAC5E;AACF;AAKO,MAAM,iBAGH,cAAgC;AAAA,EAHnC;AAAA;AAOL;AAAA;AAAA;AAAA,wBAAS,QAAO;AAAA;AAAA;AAAA,EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,SAAuC;AAAA,MAChD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAKO,MAAM,eAGH,cAAgC;AAAA,EAHnC;AAAA;AAOL;AAAA;AAAA;AAAA,wBAAS,QAAO;AAAA;AAAA;AAAA,EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,OAAqC,EAAE,YAAY,WAAW,CAAC;AAAA,EAC5E;AACF;AAKO,MAAM,gBAGH,cAAgC;AAAA,EAHnC;AAAA;AAOL;AAAA;AAAA;AAAA,wBAAS,QAAO;AAAA;AAAA;AAAA,EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,QAAsC;AAAA,MAC/C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAKO,MAAM,cAGH,cAAgC;AAAA,EAHnC;AAAA;AAOL;AAAA;AAAA;AAAA,wBAAS,QAAO;AAAA;AAAA;AAAA,EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,MAAoC,EAAE,YAAY,WAAW,CAAC;AAAA,EAC3E;AACF;AAKO,MAAM,aAIH,cAA4C;AAAA,EAJ/C;AAAA;AAQL;AAAA;AAAA;AAAA,wBAAS,QAAO;AAAA;AAAA;AAAA,EAGhB,IAAI,OAAsB;AACxB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,KAA+C;AAAA,MACxD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAKO,MAAM,gBAUH,cAA4C;AAAA;AAAA;AAAA;AAAA,EAcpD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAGG;AACD,UAAM,EAAE,WAAW,CAAC;AAjBtB;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS,QAAO;AAad,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,IAAI,OAAsB;AACxB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,WAAW,OAAO;AAAA,QACvB,WAAW,OAAO,QAAQ,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM;AAAA,UACrD;AAAA,UACA;AAAA,YACE,WAAW,EAAE;AAAA,YACb,UAAU,EAAE,eAAe,aAAa,OAAO;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,QAA0D;AAAA,MACnE,YAAY;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAKO,MAAM,iBAGH,cAAgC;AAAA;AAAA;AAAA;AAAA,EAcxC,YAAY,EAAE,YAAY,MAAM,GAA4C;AAC1E,UAAM,EAAE,WAAW,CAAC;AAXtB;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS,QAAO;AAOd,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,OAAsB;AACxB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,WAAO,2BAAa,KAAK,KAA2C;AAAA,IACtE;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,SAAuC;AAAA,MAChD,YAAY;AAAA,MACZ,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAKO,MAAM,eAIH,cAAgC;AAAA;AAAA;AAAA;AAAA,EAcxC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAGG;AACD,UAAM,EAAE,WAAW,CAAC;AAjBtB;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS,QAAO;AAad,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,IAAI,OAAsB;AACxB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,OAA8C;AAAA,MACvD,YAAY;AAAA,MACZ,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AACF;AAKO,MAAM,gBAMH,cAA4C;AAAA;AAAA;AAAA;AAAA,EAmBpD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,EAAE,WAAW,CAAC;AAxBtB;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS,QAAO;AAed,QAAK,IAAI,eAAoC,YAAY;AACvD,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAC9D;AACA,QAAK,MAAM,eAAoC,YAAY;AACzD,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAChE;AACA,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,OAAsB;AACxB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA;AAAA,MAEX,MAAM,KAAK,IAAI;AAAA,MACf,QAAQ;AAAA,QACN,WAAW,KAAK,MAAM;AAAA,QACtB,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,QAA8D;AAAA,MACvE,YAAY;AAAA,MACZ,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAKO,MAAM,eAKH,cAA4C;AAAA;AAAA;AAAA;AAAA,EAcpD,YAAY,EAAE,YAAY,QAAQ,GAA2C;AAC3E,UAAM,EAAE,WAAW,CAAC;AAXtB;AAAA;AAAA;AAAA,wBAAS;AAKT;AAAA;AAAA;AAAA,wBAAS,QAAO;AAOd,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,IAAI,OAAsB;AACxB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAI;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,IAAI,OAAwC;AAAA,MACjD,YAAY;AAAA,MACZ,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AACF;", "names": []}