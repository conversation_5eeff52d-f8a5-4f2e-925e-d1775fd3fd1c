{"version": 3, "sources": ["../../../src/values/validator.ts"], "sourcesContent": ["import { Expand } from \"../type_utils.js\";\nimport { GenericId } from \"./index.js\";\nimport {\n  OptionalProperty,\n  VAny,\n  VArray,\n  VBoolean,\n  VBytes,\n  VFloat64,\n  VId,\n  VInt64,\n  VLiteral,\n  VNull,\n  VObject,\n  VOptional,\n  VRecord,\n  VString,\n  VUnion,\n  Validator,\n} from \"./validators.js\";\n\n/**\n * The type that all validators must extend.\n *\n * @public\n */\nexport type GenericValidator = Validator<any, any, any>;\n\nexport function isValidator(v: any): v is GenericValidator {\n  return !!v.isConvexValidator;\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport function asObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n>(\n  obj: V,\n): V extends Validator<any, any, any>\n  ? V\n  : V extends PropertyValidators\n    ? Validator<ObjectType<V>>\n    : never {\n  if (isValidator(obj)) {\n    return obj as any;\n  } else {\n    return v.object(obj as PropertyValidators) as any;\n  }\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport type AsObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n> =\n  V extends Validator<any, any, any>\n    ? V\n    : V extends PropertyValidators\n      ? Validator<ObjectType<V>>\n      : never;\n\n/**\n * The validator builder.\n *\n * This builder allows you to build validators for Convex values.\n *\n * Validators can be used in [schema definitions](https://docs.convex.dev/database/schemas)\n * and as input validators for Convex functions.\n *\n * @public\n */\nexport const v = {\n  /**\n   * Validates that the value corresponds to an ID of a document in given table.\n   * @param tableName The name of the table.\n   */\n  id: <TableName extends string>(tableName: TableName) => {\n    return new VId<GenericId<TableName>>({\n      isOptional: \"required\",\n      tableName,\n    });\n  },\n\n  /**\n   * Validates that the value is of type Null.\n   */\n  null: () => {\n    return new VNull({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   *\n   * Alias for `v.float64()`\n   */\n  number: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   */\n  float64: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * @deprecated Use `v.int64()` instead\n   */\n  bigint: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Int64 (BigInt in JS).\n   */\n  int64: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type Boolean.\n   */\n  boolean: () => {\n    return new VBoolean({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type String.\n   */\n  string: () => {\n    return new VString({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Bytes (constructed in JS via `ArrayBuffer`).\n   */\n  bytes: () => {\n    return new VBytes({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is equal to the given literal value.\n   * @param literal The literal value to compare against.\n   */\n  literal: <T extends string | number | bigint | boolean>(literal: T) => {\n    return new VLiteral<T>({ isOptional: \"required\", value: literal });\n  },\n\n  /**\n   * Validates that the value is an Array of the given element type.\n   * @param element The validator for the elements of the array.\n   */\n  array: <T extends Validator<any, \"required\", any>>(element: T) => {\n    return new VArray<T[\"type\"][], T>({ isOptional: \"required\", element });\n  },\n\n  /**\n   * Validates that the value is an Object with the given properties.\n   * @param fields An object specifying the validator for each property.\n   */\n  object: <T extends PropertyValidators>(fields: T) => {\n    return new VObject<ObjectType<T>, T>({ isOptional: \"required\", fields });\n  },\n\n  /**\n   * Validates that the value is a Record with keys and values that match the given types.\n   * @param keys The validator for the keys of the record. This cannot contain string literals.\n   * @param values The validator for the values of the record.\n   */\n  record: <\n    Key extends Validator<string, \"required\", any>,\n    Value extends Validator<any, \"required\", any>,\n  >(\n    keys: Key,\n    values: Value,\n  ) => {\n    return new VRecord<Record<Infer<Key>, Value[\"type\"]>, Key, Value>({\n      isOptional: \"required\",\n      key: keys,\n      value: values,\n    });\n  },\n\n  /**\n   * Validates that the value matches one of the given validators.\n   * @param members The validators to match against.\n   */\n  union: <T extends Validator<any, \"required\", any>[]>(...members: T) => {\n    return new VUnion<T[number][\"type\"], T>({\n      isOptional: \"required\",\n      members,\n    });\n  },\n\n  /**\n   * Does not validate the value.\n   */\n  any: () => {\n    return new VAny({ isOptional: \"required\" });\n  },\n\n  /**\n   * Allows not specifying a value for a property in an Object.\n   * @param value The property value validator to make optional.\n   *\n   * ```typescript\n   * const objectWithOptionalFields = v.object({\n   *   requiredField: v.string(),\n   *   optionalField: v.optional(v.string()),\n   * });\n   * ```\n   */\n  optional: <T extends GenericValidator>(value: T) => {\n    return value.asOptional() as VOptional<T>;\n  },\n};\n\n/**\n * Validators for each property of an object.\n *\n * This is represented as an object mapping the property name to its\n * {@link Validator}.\n *\n * @public\n */\nexport type PropertyValidators = Record<\n  string,\n  Validator<any, OptionalProperty, any>\n>;\n\n/**\n * Compute the type of an object from {@link PropertyValidators}.\n *\n * @public\n */\nexport type ObjectType<Fields extends PropertyValidators> = Expand<\n  // Map each key to the corresponding property validator's type making\n  // the optional ones optional.\n  {\n    // This `Exclude<..., undefined>` does nothing unless\n    // the tsconfig.json option `\"exactOptionalPropertyTypes\": true,`\n    // is used. When it is it results in a more accurate type.\n    // When it is not the `Exclude` removes `undefined` but it is\n    // added again by the optional property.\n    [Property in OptionalKeys<Fields>]?: Exclude<\n      Infer<Fields[Property]>,\n      undefined\n    >;\n  } & {\n    [Property in RequiredKeys<Fields>]: Infer<Fields[Property]>;\n  }\n>;\n\ntype OptionalKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  {\n    [Property in keyof PropertyValidators]: PropertyValidators[Property][\"isOptional\"] extends \"optional\"\n      ? Property\n      : never;\n  }[keyof PropertyValidators];\n\ntype RequiredKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  Exclude<keyof PropertyValidators, OptionalKeys<PropertyValidators>>;\n\n/**\n * Extract a TypeScript type from a validator.\n *\n * Example usage:\n * ```ts\n * const objectSchema = v.object({\n *   property: v.string(),\n * });\n * type MyObject = Infer<typeof objectSchema>; // { property: string }\n * ```\n * @typeParam V - The type of a {@link Validator} constructed with {@link v}.\n *\n * @public\n */\nexport type Infer<T extends Validator<any, OptionalProperty, any>> = T[\"type\"];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,wBAiBO;AASA,SAAS,YAAYA,IAA+B;AACzD,SAAO,CAAC,CAACA,GAAE;AACb;AAQO,SAAS,kBAGd,KAKU;AACV,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,EAAE,OAAO,GAAyB;AAAA,EAC3C;AACF;AA2BO,MAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,IAAI,CAA2B,cAAyB;AACtD,WAAO,IAAI,sBAA0B;AAAA,MACnC,YAAY;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM;AACV,WAAO,IAAI,wBAAM,EAAE,YAAY,WAAW,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,WAAO,IAAI,2BAAS,EAAE,YAAY,WAAW,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACb,WAAO,IAAI,2BAAS,EAAE,YAAY,WAAW,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM;AACZ,WAAO,IAAI,yBAAO,EAAE,YAAY,WAAW,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM;AACX,WAAO,IAAI,yBAAO,EAAE,YAAY,WAAW,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACb,WAAO,IAAI,2BAAS,EAAE,YAAY,WAAW,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM;AACZ,WAAO,IAAI,0BAAQ,EAAE,YAAY,WAAW,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM;AACX,WAAO,IAAI,yBAAO,EAAE,YAAY,WAAW,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,CAA+C,YAAe;AACrE,WAAO,IAAI,2BAAY,EAAE,YAAY,YAAY,OAAO,QAAQ,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,CAA4C,YAAe;AAChE,WAAO,IAAI,yBAAuB,EAAE,YAAY,YAAY,QAAQ,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,CAA+B,WAAc;AACnD,WAAO,IAAI,0BAA0B,EAAE,YAAY,YAAY,OAAO,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,CAIN,MACA,WACG;AACH,WAAO,IAAI,0BAAuD;AAAA,MAChE,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,IAAiD,YAAe;AACrE,WAAO,IAAI,yBAA6B;AAAA,MACtC,YAAY;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AACT,WAAO,IAAI,uBAAK,EAAE,YAAY,WAAW,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,UAAU,CAA6B,UAAa;AAClD,WAAO,MAAM,WAAW;AAAA,EAC1B;AACF;", "names": ["v"]}