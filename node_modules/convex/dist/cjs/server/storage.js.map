{"version": 3, "sources": ["../../../src/server/storage.ts"], "sourcesContent": ["import { GenericId } from \"../values/index.js\";\n\n/**\n * A reference to a file in storage.\n *\n * This is used in the {@link StorageReader} and {@link StorageWriter} which are accessible in\n * Convex queries and mutations via {@link QueryCtx} and {@link MutationCtx} respectively.\n *\n * @public\n */\nexport type StorageId = string;\nexport type FileStorageId = GenericId<\"_storage\"> | StorageId;\n/**\n * Metadata for a single file as returned by {@link StorageReader.getMetadata | storage.getMetadata}.\n *\n * @public\n */\nexport type FileMetadata = {\n  /**\n   * ID for referencing the file (eg. via {@link StorageReader.getUrl | storage.getUrl})\n   */\n  storageId: StorageId;\n  /**\n   * Hex encoded sha256 checksum of file contents\n   */\n  sha256: string;\n  /**\n   * Size of the file in bytes\n   */\n  size: number;\n  /**\n   * ContentType of the file if it was provided on upload\n   */\n  contentType: string | null;\n};\n\n/**\n * An interface to read files from storage within Convex query functions.\n *\n * @public\n */\nexport interface StorageReader {\n  /**\n   * Get the URL for a file in storage by its `Id<\"_storage\">`.\n   *\n   * The GET response includes a standard HTTP Digest header with a sha256 checksum.\n   *\n   * @param storageId - The `Id<\"_storage\">` of the file to fetch from Convex storage.\n   * @returns - A url which fetches the file via an HTTP GET, or `null` if it no longer exists.\n   */\n  getUrl(storageId: GenericId<\"_storage\">): Promise<string | null>;\n\n  /**\n   * @deprecated Passing a string is deprecated, use `storage.getUrl(Id<\"_storage\">)` instead.\n   *\n   * Get the URL for a file in storage by its {@link StorageId}.\n   *\n   * The GET response includes a standard HTTP Digest header with a sha256 checksum.\n   *\n   * @param storageId - The {@link StorageId} of the file to fetch from Convex storage.\n   * @returns - A url which fetches the file via an HTTP GET, or `null` if it no longer exists.\n   */\n  getUrl<T extends StorageId>(\n    storageId: T extends { __tableName: any } ? never : T,\n  ): Promise<string | null>;\n\n  /**\n   * @deprecated This function is deprecated, use `db.system.get(Id<\"_storage\">)` instead.\n   *\n   * Get metadata for a file.\n   *\n   * @param storageId - The `Id<\"_storage\">` of the file.\n   * @returns - A {@link FileMetadata} object if found or `null` if not found.\n   */\n  getMetadata(storageId: GenericId<\"_storage\">): Promise<FileMetadata | null>;\n\n  /**\n   * @deprecated This function is deprecated, use `db.system.get(Id<\"_storage\">)` instead.\n   *\n   * Get metadata for a file.\n   *\n   * @param storageId - The {@link StorageId} of the file.\n   * @returns - A {@link FileMetadata} object if found or `null` if not found.\n   */\n  getMetadata<T extends StorageId>(\n    storageId: T extends { __tableName: any } ? never : T,\n  ): Promise<FileMetadata | null>;\n}\n\n/**\n * An interface to write files to storage within Convex mutation functions.\n *\n * @public\n */\nexport interface StorageWriter extends StorageReader {\n  /**\n   * Fetch a short-lived URL for uploading a file into storage.\n   *\n   * Upon a POST request to this URL, the endpoint will return a JSON object containing a newly allocated `Id<\"_storage\">`.\n   *\n   * The POST URL accepts an optional standard HTTP Digest header with a sha256 checksum.\n   *\n   * @returns - A url that allows file upload via an HTTP POST.\n   */\n  generateUploadUrl(): Promise<string>;\n  /**\n   * Delete a file from Convex storage.\n   *\n   * Once a file is deleted, any URLs previously generated by {@link StorageReader.getUrl} will return 404s.\n   *\n   * @param storageId - The `Id<\"_storage\">` of the file to delete from Convex storage.\n   */\n  delete(storageId: GenericId<\"_storage\">): Promise<void>;\n\n  /**\n   * @deprecated Passing a string is deprecated, use `storage.delete(Id<\"_storage\">)` instead.\n   *\n   * Delete a file from Convex storage.\n   *\n   * Once a file is deleted, any URLs previously generated by {@link StorageReader.getUrl} will return 404s.\n   *\n   * @param storageId - The {@link StorageId} of the file to delete from Convex storage.\n   */\n  delete<T extends StorageId>(\n    storageId: T extends { __tableName: any } ? never : T,\n  ): Promise<void>;\n}\n\n/**\n * An interface to read and write files to storage within Convex actions and HTTP actions.\n *\n * @public\n */\nexport interface StorageActionWriter extends StorageWriter {\n  /**\n   * Get a Blob containing the file associated with the provided `Id<\"_storage\">`, or `null` if there is no file.\n   */\n  get(storageId: GenericId<\"_storage\">): Promise<Blob | null>;\n\n  /**\n   * @deprecated Passing a string is deprecated, use `storage.get(Id<\"_storage\">)` instead.\n   *\n   * Get a Blob containing the file associated with the provided {@link StorageId}, or `null` if there is no file.\n   */\n  get<T extends StorageId>(\n    storageId: T extends { __tableName: any } ? never : T,\n  ): Promise<Blob | null>;\n  /**\n   * Store the file contained in the Blob.\n   *\n   * If provided, this will verify the sha256 checksum matches the contents of the file.\n   */\n  store(\n    blob: Blob,\n    options?: { sha256?: string },\n  ): Promise<GenericId<\"_storage\">>;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}