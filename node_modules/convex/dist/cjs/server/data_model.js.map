{"version": 3, "sources": ["../../../src/server/data_model.ts"], "sourcesContent": ["import { Value } from \"../values/index.js\";\n\n// Document Types  /////////////////////////////////////////////////////////////\n\n/**\n * A document stored in Convex.\n * @public\n */\nexport type GenericDocument = Record<string, Value>;\n\n/**\n * A type describing all of the document fields in a table.\n *\n * These can either be field names (like \"name\") or references to fields on\n * nested objects (like \"properties.name\").\n * @public\n */\nexport type GenericFieldPaths = string;\n\n// Index Types  ///////////////////////////////////////////////////////////////\n\n/**\n * A type describing the ordered fields in an index.\n *\n * These can either be field names (like \"name\") or references to fields on\n * nested objects (like \"properties.name\").\n * @public\n */\nexport type GenericIndexFields = string[];\n\n/**\n * A type describing the indexes in a table.\n *\n * It's an object mapping each index name to the fields in the index.\n * @public\n */\nexport type GenericTableIndexes = Record<string, GenericIndexFields>;\n\n/**\n * A type describing the configuration of a search index.\n * @public\n */\nexport type GenericSearchIndexConfig = {\n  searchField: string;\n  filterFields: string;\n};\n\n/**\n * A type describing all of the search indexes in a table.\n *\n * This is an object mapping each index name to the config for the index.\n * @public\n */\nexport type GenericTableSearchIndexes = Record<\n  string,\n  GenericSearchIndexConfig\n>;\n\n/**\n * A type describing the configuration of a vector index.\n * @public\n */\nexport type GenericVectorIndexConfig = {\n  vectorField: string;\n  dimensions: number;\n  filterFields: string;\n};\n\n/**\n * A type describing all of the vector indexes in a table.\n *\n * This is an object mapping each index name to the config for the index.\n * @public\n */\nexport type GenericTableVectorIndexes = Record<\n  string,\n  GenericVectorIndexConfig\n>;\n/**\n * If we have A | B | C, this finds A[Key] | B[Key] | C[Key], where we default to\n * `Default` if the Key isn't found.\n *\n * Conditional types apparently loop over the variants in a union, so the `T extends T`\n * is enough to force this behavior.\n * https://stackoverflow.com/questions/49401866/all-possible-keys-of-an-union-type\n */\n\ntype ValueFromUnion<T, Key, Default> = T extends T\n  ? Key extends keyof T\n    ? T[Key]\n    : Default\n  : never;\n\n/**\n * The type of a field in a document.\n *\n * Note that this supports both simple fields like \"name\" and nested fields like\n * \"properties.name\".\n *\n * If the field is not present in the document it is considered to be `undefined`.\n *\n * @public\n */\nexport type FieldTypeFromFieldPath<\n  Document extends GenericDocument,\n  FieldPath extends string,\n> =\n  FieldTypeFromFieldPathInner<Document, FieldPath> extends Value | undefined\n    ? FieldTypeFromFieldPathInner<Document, FieldPath>\n    : Value | undefined;\n\n/**\n * The inner type of {@link FieldTypeFromFieldPath}.\n *\n * It's wrapped in a helper to coerce the type to `Value | undefined` since some\n * versions of TypeScript fail to infer this type correctly.\n *\n * @public\n */\nexport type FieldTypeFromFieldPathInner<\n  Document extends GenericDocument,\n  FieldPath extends string,\n> = FieldPath extends `${infer First}.${infer Second}`\n  ? ValueFromUnion<\n      Document,\n      First,\n      Record<never, never>\n    > extends infer FieldValue\n    ? // The fact that `extends infer` extracts the generic document out of a union of a\n      // Value and record of Values (GenericDocument) is due to the feature\n      // \"Distributive Conditional Types\" in the TypeScript Handbook:\n      // https://www.typescriptlang.org/docs/handbook/2/conditional-types.html#distributive-conditional-types\n      FieldValue extends GenericDocument\n      ? FieldTypeFromFieldPath<FieldValue, Second>\n      : undefined\n    : undefined\n  : ValueFromUnion<Document, FieldPath, undefined>;\n\n// Table Types /////////////////////////////////////////////////////////////////\n\n/**\n * A type describing the document type and indexes in a table.\n * @public\n */\nexport type GenericTableInfo = {\n  document: GenericDocument;\n  fieldPaths: GenericFieldPaths;\n  indexes: GenericTableIndexes;\n  searchIndexes: GenericTableSearchIndexes;\n  vectorIndexes: GenericTableVectorIndexes;\n};\n\n/**\n * The type of a document in a table for a given {@link GenericTableInfo}.\n * @public\n */\nexport type DocumentByInfo<TableInfo extends GenericTableInfo> =\n  TableInfo[\"document\"];\n\n/**\n * The field paths in a table for a given {@link GenericTableInfo}.\n *\n * These can either be field names (like \"name\") or references to fields on\n * nested objects (like \"properties.name\").\n * @public\n */\nexport type FieldPaths<TableInfo extends GenericTableInfo> =\n  TableInfo[\"fieldPaths\"];\n\n/**\n * The database indexes in a table for a given {@link GenericTableInfo}.\n *\n * This will be an object mapping index names to the fields in the index.\n * @public\n */\nexport type Indexes<TableInfo extends GenericTableInfo> = TableInfo[\"indexes\"];\n\n/**\n * The names of indexes in a table for a given {@link GenericTableInfo}.\n * @public\n */\nexport type IndexNames<TableInfo extends GenericTableInfo> =\n  keyof Indexes<TableInfo>;\n\n/**\n * Extract the fields of an index from a {@link GenericTableInfo} by name.\n * @public\n */\nexport type NamedIndex<\n  TableInfo extends GenericTableInfo,\n  IndexName extends IndexNames<TableInfo>,\n> = Indexes<TableInfo>[IndexName];\n\n/**\n * The search indexes in a table for a given {@link GenericTableInfo}.\n *\n * This will be an object mapping index names to the search index config.\n * @public\n */\nexport type SearchIndexes<TableInfo extends GenericTableInfo> =\n  TableInfo[\"searchIndexes\"];\n\n/**\n * The names of search indexes in a table for a given {@link GenericTableInfo}.\n * @public\n */\nexport type SearchIndexNames<TableInfo extends GenericTableInfo> =\n  keyof SearchIndexes<TableInfo>;\n\n/**\n * Extract the config of a search index from a {@link GenericTableInfo} by name.\n * @public\n */\nexport type NamedSearchIndex<\n  TableInfo extends GenericTableInfo,\n  IndexName extends SearchIndexNames<TableInfo>,\n> = SearchIndexes<TableInfo>[IndexName];\n\n/**\n * The vector indexes in a table for a given {@link GenericTableInfo}.\n *\n * This will be an object mapping index names to the vector index config.\n * @public\n */\nexport type VectorIndexes<TableInfo extends GenericTableInfo> =\n  TableInfo[\"vectorIndexes\"];\n\n/**\n * The names of vector indexes in a table for a given {@link GenericTableInfo}.\n * @public\n */\nexport type VectorIndexNames<TableInfo extends GenericTableInfo> =\n  keyof VectorIndexes<TableInfo>;\n\n/**\n * Extract the config of a vector index from a {@link GenericTableInfo} by name.\n * @public\n */\nexport type NamedVectorIndex<\n  TableInfo extends GenericTableInfo,\n  IndexName extends VectorIndexNames<TableInfo>,\n> = VectorIndexes<TableInfo>[IndexName];\n\n// Data Model Types ////////////////////////////////////////////////////////////\n\n/**\n * A type describing the tables in a Convex project.\n *\n * This is designed to be code generated with `npx convex dev`.\n * @public\n */\nexport type GenericDataModel = Record<string, GenericTableInfo>;\n\n/**\n * A {@link GenericDataModel} that considers documents to be `any` and does not\n * support indexes.\n *\n * This is the default before a schema is defined.\n * @public\n */\nexport type AnyDataModel = {\n  [tableName: string]: {\n    document: any;\n    fieldPaths: GenericFieldPaths;\n    indexes: {};\n    searchIndexes: {};\n    vectorIndexes: {};\n  };\n};\n\n/**\n * A type of all of the table names defined in a {@link GenericDataModel}.\n * @public\n */\nexport type TableNamesInDataModel<DataModel extends GenericDataModel> =\n  keyof DataModel & string;\n\n/**\n * Extract the `TableInfo` for a table in a {@link GenericDataModel} by table\n * name.\n *\n * @public\n */\nexport type NamedTableInfo<\n  DataModel extends GenericDataModel,\n  TableName extends keyof DataModel,\n> = DataModel[TableName];\n\n/**\n * The type of a document in a {@link GenericDataModel} by table name.\n * @public\n */\nexport type DocumentByName<\n  DataModel extends GenericDataModel,\n  TableName extends TableNamesInDataModel<DataModel>,\n> = DataModel[TableName][\"document\"];\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}