{"version": 3, "sources": ["../../../src/server/authentication.ts"], "sourcesContent": ["import { JSONValue } from \"../values/index.js\";\n\n/**\n * Information about an authenticated user, derived from a\n * [JWT](https://datatracker.ietf.org/doc/html/rfc7519).\n *\n * The only fields guaranteed to be present are\n * {@link UserIdentity.tokenIdentifier} and {@link UserIdentity.issuer}. All\n * remaining fields may or may not be present depending on the information given\n * by the identity provider.\n *\n * The explicitly listed fields are derived from the OpenID Connect (OIDC) standard fields,\n * see the [OIDC specification](https://openid.net/specs/openid-connect-core-1_0.html#StandardClaims)\n * for more information on these fields.\n *\n * Any additional fields are custom claims that may be present in the JWT,\n * and their type depends on your identity provider configuration. If you\n * know the type of the field, you can assert it in TypeScript like this\n * (for example as a `string`):\n *\n * ```typescript\n * const identity = await ctx.auth.getUserIdentity();\n * if (identity === null) {\n *   return null;\n * }\n * const customClaim = identity.custom_claim as string;\n * ```\n *\n * @public\n */\nexport interface UserIdentity {\n  /**\n   * A stable and globally unique string for this identity (i.e. no other\n   * user, even from a different identity provider, will have the same string.)\n   *\n   * JWT claims: `sub` + `iss`\n   */\n  readonly tokenIdentifier: string;\n\n  /**\n   * Identifier for the end-user from the identity provider, not necessarily\n   * unique across different providers.\n   *\n   * JWT claim: `sub`\n   */\n  readonly subject: string;\n\n  /**\n   * The hostname of the identity provider used to authenticate this user.\n   *\n   * JWT claim: `iss`\n   */\n  readonly issuer: string;\n\n  /**\n   * JWT claim: `name`\n   */\n  readonly name?: string;\n\n  /**\n   * JWT claim: `given_name`\n   */\n  readonly givenName?: string;\n\n  /**\n   * JWT claim: `family_name`\n   */\n  readonly familyName?: string;\n\n  /**\n   * JWT claim: `nickname`\n   */\n  readonly nickname?: string;\n\n  /**\n   * JWT claim: `preferred_username`\n   */\n  readonly preferredUsername?: string;\n\n  /**\n   * JWT claim: `profile`\n   */\n  readonly profileUrl?: string;\n\n  /**\n   * JWT claim: `picture`\n   */\n  readonly pictureUrl?: string;\n\n  /**\n   * JWT claim: `email`\n   */\n  readonly email?: string;\n\n  /**\n   * JWT claim: `email_verified`\n   */\n  readonly emailVerified?: boolean;\n\n  /**\n   * JWT claim: `gender`\n   */\n  readonly gender?: string;\n\n  /**\n   * JWT claim: `birthdate`\n   */\n  readonly birthday?: string;\n\n  /**\n   * JWT claim: `zoneinfo`\n   */\n  readonly timezone?: string;\n\n  /**\n   * JWT claim: `locale`\n   */\n  readonly language?: string;\n\n  /**\n   * JWT claim: `phone_number`\n   */\n  readonly phoneNumber?: string;\n\n  /**\n   * JWT claim: `phone_number_verified`\n   */\n  readonly phoneNumberVerified?: boolean;\n\n  /**\n   * JWT claim: `address`\n   */\n  readonly address?: string;\n\n  /**\n   * JWT claim: `updated_at`\n   */\n  readonly updatedAt?: string;\n\n  /**\n   * Any custom claims.\n   */\n  [key: string]: JSONValue | undefined;\n}\n\nexport type UserIdentityAttributes = Omit<\n  UserIdentity,\n  // tokenIdentifier is always generated by us\n  \"tokenIdentifier\"\n>;\n\n/**\n * An interface to access information about the currently authenticated user\n * within Convex query and mutation functions.\n *\n * @public\n */\nexport interface Auth {\n  /**\n   * Get details about the currently authenticated user.\n   *\n   * @returns A promise that resolves to a {@link UserIdentity} if the Convex\n   * client was configured with a valid ID token, or if not, will:\n   * + returns `null` on Convex queries, mutations, actions.\n   * + `throw` on HTTP Actions.\n   */\n  getUserIdentity(): Promise<UserIdentity | null>;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}