{"version": 3, "sources": ["../../../src/server/system_fields.ts"], "sourcesContent": ["import { GenericId } from \"../values/index.js\";\nimport { BetterOmit, Expand } from \"../type_utils.js\";\nimport { GenericDocument } from \"./data_model.js\";\n\n/**\n * The fields that Convex automatically adds to documents, not including `_id`.\n *\n * This is an object type mapping field name to field type.\n * @public\n */\nexport type SystemFields = {\n  _creationTime: number;\n};\n\n/**\n * The `_id` field that Convex automatically adds to documents.\n * @public\n */\nexport type IdField<TableName extends string> = {\n  _id: GenericId<TableName>;\n};\n\n/**\n * A Convex document with the system fields like `_id` and `_creationTime` omitted.\n *\n * @public\n */\nexport type WithoutSystemFields<Document extends GenericDocument> = Expand<\n  BetterOmit<Document, keyof SystemFields | \"_id\">\n>;\n\n/**\n * A Convex document with the system fields like `_id` and `_creationTime` optional.\n *\n * @public\n */\nexport type WithOptionalSystemFields<Document extends GenericDocument> = Expand<\n  WithoutSystemFields<Document> &\n    Partial<Pick<Document, keyof SystemFields | \"_id\">>\n>;\n\n/**\n * The indexes that Convex automatically adds to every table.\n *\n * This is an object mapping index names to index field paths.\n * @public\n */\nexport type SystemIndexes = {\n  // Note `db.get(id)` is simpler and equivalent to a query on `by_id`.\n  // Unless the query is being built dynamically, or doing manual pagination.\n  by_id: [\"_id\"];\n\n  by_creation_time: [\"_creationTime\"];\n};\n\n/**\n * Convex automatically appends \"_creationTime\" to the end of every index to\n * break ties if all of the other fields are identical.\n * @public\n */\nexport type IndexTiebreakerField = \"_creationTime\";\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}