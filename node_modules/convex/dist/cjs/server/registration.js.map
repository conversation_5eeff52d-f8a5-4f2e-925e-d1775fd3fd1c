{"version": 3, "sources": ["../../../src/server/registration.ts"], "sourcesContent": ["import {\n  Auth,\n  GenericData<PERSON>Reader,\n  GenericDatabaseReaderWithTable,\n  GenericDatabaseWriter,\n  GenericDatabaseWriterWithTable,\n  StorageActionWriter,\n  StorageReader,\n  StorageWriter,\n} from \"./index.js\";\nimport {\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  ValidatorTypeToReturnType,\n} from \"../server/api.js\";\nimport {\n  GenericValidator,\n  Infer,\n  ObjectType,\n  PropertyValidators,\n} from \"../values/validator.js\";\nimport { Id } from \"../values/value.js\";\nimport {\n  GenericDataModel,\n  NamedTableInfo,\n  TableNamesInDataModel,\n  VectorIndexNames,\n} from \"./data_model.js\";\nimport { Scheduler } from \"./scheduler.js\";\nimport { VectorSearchQuery } from \"./vector_search.js\";\nimport { Expand } from \"../type_utils.js\";\nimport { Validator } from \"../values/validators.js\";\n\n/**\n * A set of services for use within Convex mutation functions.\n *\n * The mutation context is passed as the first argument to any Convex mutation\n * function run on the server.\n *\n * If you're using code generation, use the `MutationCtx` type in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @public\n */\nexport interface GenericMutationCtx<DataModel extends GenericDataModel> {\n  /**\n   * A utility for reading and writing data in the database.\n   */\n  db: GenericDatabaseWriter<DataModel>;\n\n  /**\n   * Information about the currently authenticated user.\n   */\n  auth: Auth;\n\n  /**\n   * A utility for reading and writing files in storage.\n   */\n  storage: StorageWriter;\n\n  /**\n   * A utility for scheduling Convex functions to run in the future.\n   */\n  scheduler: Scheduler;\n\n  /**\n   * Call a query function within the same transaction.\n   *\n   * NOTE: often you can call the query's function directly instead of using this.\n   * `runQuery` incurs overhead of running argument and return value validation,\n   * and creating a new isolated JS context.\n   */\n  runQuery: <Query extends FunctionReference<\"query\", \"public\" | \"internal\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ) => Promise<FunctionReturnType<Query>>;\n\n  /**\n   * Call a mutation function within the same transaction.\n   *\n   * NOTE: often you can call the mutation's function directly instead of using this.\n   * `runMutation` incurs overhead of running argument and return value validation,\n   * and creating a new isolated JS context.\n   *\n   * The mutation runs in a sub-transaction, so if the mutation throws an error,\n   * all of its writes will be rolled back. Additionally, a successful mutation's\n   * writes will be serializable with other writes in the transaction.\n   */\n  runMutation: <\n    Mutation extends FunctionReference<\"mutation\", \"public\" | \"internal\">,\n  >(\n    mutation: Mutation,\n    ...args: OptionalRestArgs<Mutation>\n  ) => Promise<FunctionReturnType<Mutation>>;\n}\n\n/**\n * A set of services for use within Convex mutation functions.\n *\n * The mutation context is passed as the first argument to any Convex mutation\n * function run on the server.\n *\n * If you're using code generation, use the `MutationCtx` type in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @public\n */\nexport type GenericMutationCtxWithTable<DataModel extends GenericDataModel> =\n  Omit<GenericMutationCtx<DataModel>, \"db\"> & {\n    db: GenericDatabaseWriterWithTable<DataModel>;\n  };\n\n/**\n * A set of services for use within Convex query functions.\n *\n * The query context is passed as the first argument to any Convex query\n * function run on the server.\n *\n * This differs from the {@link MutationCtx} because all of the services are\n * read-only.\n *\n *\n * @public\n */\nexport interface GenericQueryCtx<DataModel extends GenericDataModel> {\n  /**\n   * A utility for reading data in the database.\n   */\n  db: GenericDatabaseReader<DataModel>;\n\n  /**\n   * Information about the currently authenticated user.\n   */\n  auth: Auth;\n\n  /**\n   * A utility for reading files in storage.\n   */\n  storage: StorageReader;\n\n  /**\n   * Call a query function within the same transaction.\n   *\n   * NOTE: often you can call the query's function directly instead of using this.\n   * `runQuery` incurs overhead of running argument and return value validation,\n   * and creating a new isolated JS context.\n   */\n  runQuery: <Query extends FunctionReference<\"query\", \"public\" | \"internal\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ) => Promise<FunctionReturnType<Query>>;\n}\n\n/**\n * A set of services for use within Convex query functions.\n *\n * The query context is passed as the first argument to any Convex query\n * function run on the server.\n *\n * This differs from the {@link MutationCtx} because all of the services are\n * read-only.\n *\n *\n * @public\n */\nexport type GenericQueryCtxWithTable<DataModel extends GenericDataModel> = Omit<\n  GenericQueryCtx<DataModel>,\n  \"db\"\n> & {\n  db: GenericDatabaseReaderWithTable<DataModel>;\n};\n\n/**\n * A set of services for use within Convex action functions.\n *\n * The context is passed as the first argument to any Convex action\n * run on the server.\n *\n * If you're using code generation, use the `ActionCtx` type in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @public\n */\nexport interface GenericActionCtx<DataModel extends GenericDataModel> {\n  /**\n   * Run the Convex query with the given name and arguments.\n   *\n   * Consider using an {@link internalQuery} to prevent users from calling the\n   * query directly.\n   *\n   * @param query - A {@link FunctionReference} for the query to run.\n   * @param args - The arguments to the query function.\n   * @returns A promise of the query's result.\n   */\n  runQuery<Query extends FunctionReference<\"query\", \"public\" | \"internal\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>>;\n\n  /**\n   * Run the Convex mutation with the given name and arguments.\n   *\n   * Consider using an {@link internalMutation} to prevent users from calling\n   * the mutation directly.\n   *\n   * @param mutation - A {@link FunctionReference} for the mutation to run.\n   * @param args - The arguments to the mutation function.\n   * @returns A promise of the mutation's result.\n   */\n  runMutation<\n    Mutation extends FunctionReference<\"mutation\", \"public\" | \"internal\">,\n  >(\n    mutation: Mutation,\n    ...args: OptionalRestArgs<Mutation>\n  ): Promise<FunctionReturnType<Mutation>>;\n\n  /**\n   * Run the Convex action with the given name and arguments.\n   *\n   * Consider using an {@link internalAction} to prevent users from calling the\n   * action directly.\n   *\n   * @param action - A {@link FunctionReference} for the action to run.\n   * @param args - The arguments to the action function.\n   * @returns A promise of the action's result.\n   */\n  runAction<Action extends FunctionReference<\"action\", \"public\" | \"internal\">>(\n    action: Action,\n    ...args: OptionalRestArgs<Action>\n  ): Promise<FunctionReturnType<Action>>;\n\n  /**\n   * A utility for scheduling Convex functions to run in the future.\n   */\n  scheduler: Scheduler;\n\n  /**\n   * Information about the currently authenticated user.\n   */\n  auth: Auth;\n\n  /**\n   * A utility for reading and writing files in storage.\n   */\n  storage: StorageActionWriter;\n\n  /**\n   * Run a vector search on the given table and index.\n   *\n   * @param tableName - The name of the table to query.\n   * @param indexName - The name of the vector index on the table to query.\n   * @param query - A {@link VectorSearchQuery} containing the vector to query,\n   * the number of results to return, and any filters.\n   * @returns A promise of IDs and scores for the documents with the nearest\n   * vectors\n   */\n  vectorSearch<\n    TableName extends TableNamesInDataModel<DataModel>,\n    IndexName extends VectorIndexNames<NamedTableInfo<DataModel, TableName>>,\n  >(\n    tableName: TableName,\n    indexName: IndexName,\n    query: Expand<\n      VectorSearchQuery<NamedTableInfo<DataModel, TableName>, IndexName>\n    >,\n  ): Promise<Array<{ _id: Id<TableName>; _score: number }>>;\n}\n\n/**\n * The default arguments type for a Convex query, mutation, or action function.\n *\n * Convex functions always take an arguments object that maps the argument\n * names to their values.\n *\n * @public\n */\nexport type DefaultFunctionArgs = Record<string, unknown>;\n\n/**\n * The arguments array for a function that takes arguments.\n *\n * This is an array of a single {@link DefaultFunctionArgs} element.\n */\ntype OneArgArray<ArgsObject extends DefaultFunctionArgs = DefaultFunctionArgs> =\n  [ArgsObject];\n\n/**\n * The arguments to a function that takes no arguments (just an empty array).\n */\ntype NoArgsArray = [];\n\n/**\n * An array of arguments to a Convex function.\n *\n * Convex functions can take either a single {@link DefaultFunctionArgs} object or no\n * args at all.\n *\n * @public\n */\nexport type ArgsArray = OneArgArray | NoArgsArray;\n\n/**\n * A type for the empty object `{}`.\n *\n * Note that we don't use `type EmptyObject = {}` because that matches every object.\n */\nexport type EmptyObject = Record<string, never>;\n\n/**\n * Convert an {@link ArgsArray} into a single object type.\n *\n * Empty arguments arrays are converted to {@link EmptyObject}.\n * @public\n */\nexport type ArgsArrayToObject<Args extends ArgsArray> =\n  Args extends OneArgArray<infer ArgsObject> ? ArgsObject : EmptyObject;\n\n/**\n * A type representing the visibility of a Convex function.\n *\n * @public\n */\nexport type FunctionVisibility = \"public\" | \"internal\";\n\n/**\n * Given a {@link FunctionVisibility}, should this function have `isPublic: true`\n * or `isInternal: true`?\n */\ntype VisibilityProperties<Visiblity extends FunctionVisibility> =\n  Visiblity extends \"public\"\n    ? {\n        isPublic: true;\n      }\n    : {\n        isInternal: true;\n      };\n\n/**\n * A mutation function that is part of this app.\n *\n * You can create a mutation by wrapping your function in\n * {@link mutationGeneric} or {@link internalMutationGeneric} and exporting it.\n *\n * @public\n */\nexport type RegisteredMutation<\n  Visibility extends FunctionVisibility,\n  Args extends DefaultFunctionArgs,\n  Returns,\n> = {\n  isConvexFunction: true;\n  isMutation: true;\n\n  /** @internal */\n  invokeMutation(argsStr: string): Promise<string>;\n\n  /** @internal */\n  exportArgs(): string;\n\n  /** @internal */\n  exportReturns(): string;\n\n  /** @internal */\n  _handler: (ctx: GenericMutationCtx<any>, args: Args) => Returns;\n} & VisibilityProperties<Visibility>;\n\n/**\n * A query function that is part of this app.\n *\n * You can create a query by wrapping your function in\n * {@link queryGeneric} or {@link internalQueryGeneric} and exporting it.\n *\n * @public\n */\nexport type RegisteredQuery<\n  Visibility extends FunctionVisibility,\n  Args extends DefaultFunctionArgs,\n  Returns,\n> = {\n  isConvexFunction: true;\n  isQuery: true;\n\n  /** @internal */\n  invokeQuery(argsStr: string): Promise<string>;\n\n  /** @internal */\n  exportArgs(): string;\n\n  /** @internal */\n  exportReturns(): string;\n\n  /** @internal */\n  _handler: (ctx: GenericQueryCtx<any>, args: Args) => Returns;\n} & VisibilityProperties<Visibility>;\n\n/**\n * An action that is part of this app.\n *\n * You can create an action by wrapping your function in\n * {@link actionGeneric} or {@link internalActionGeneric} and exporting it.\n *\n * @public\n */\nexport type RegisteredAction<\n  Visibility extends FunctionVisibility,\n  Args extends DefaultFunctionArgs,\n  Returns,\n> = {\n  isConvexFunction: true;\n  isAction: true;\n\n  /** @internal */\n  invokeAction(requestId: string, argsStr: string): Promise<string>;\n\n  /** @internal */\n  exportArgs(): string;\n\n  /** @internal */\n  exportReturns(): string;\n\n  /** @internal */\n  _handler: (ctx: GenericActionCtx<any>, args: Args) => Returns;\n} & VisibilityProperties<Visibility>;\n\n/**\n * An HTTP action that is part of this app's public API.\n *\n * You can create public HTTP actions by wrapping your function in\n * {@link httpActionGeneric} and exporting it.\n *\n * @public\n */\nexport type PublicHttpAction = {\n  isHttp: true;\n\n  /** @internal */\n  invokeHttpAction(request: Request): Promise<Response>;\n  /** @internal */\n  _handler: (ctx: GenericActionCtx<any>, request: Request) => Promise<Response>;\n};\n\n/**\n * @deprecated -- See the type definition for `MutationBuilder` or similar for\n * the types used for defining Convex functions.\n *\n * The definition of a Convex query, mutation, or action function without\n * argument validation.\n *\n * Convex functions always take a context object as their first argument\n * and an (optional) args object as their second argument.\n *\n * This can be written as a function like:\n * ```js\n * import { query } from \"./_generated/server\";\n *\n * export const func = query(({ db }, { arg }) => {...});\n * ```\n * or as an object like:\n *\n * ```js\n * import { query } from \"./_generated/server\";\n *\n * export const func = query({\n *   handler: ({ db }, { arg }) => {...},\n * });\n * ```\n * See {@link ValidatedFunction} to add argument validation.\n *\n * @public\n */\nexport type UnvalidatedFunction<Ctx, Args extends ArgsArray, Returns> =\n  | ((ctx: Ctx, ...args: Args) => Returns)\n  | {\n      handler: (ctx: Ctx, ...args: Args) => Returns;\n    };\n\n/**\n * @deprecated -- See the type definition for `MutationBuilder` or similar for\n * the types used for defining Convex functions.\n *\n * The definition of a Convex query, mutation, or action function with argument\n * validation.\n *\n * Argument validation allows you to assert that the arguments to this function\n * are the expected type.\n *\n * Example:\n *\n * ```js\n * import { query } from \"./_generated/server\";\n * import { v } from \"convex/values\";\n *\n * export const func = query({\n *   args: {\n *     arg: v.string()\n *   },\n *   handler: ({ db }, { arg }) => {...},\n * });\n * ```\n *\n * **For security, argument validation should be added to all public functions in\n * production apps.**\n *\n * See {@link UnvalidatedFunction} for functions without argument validation.\n * @public\n */\nexport interface ValidatedFunction<\n  Ctx,\n  ArgsValidator extends PropertyValidators,\n  Returns,\n> {\n  /**\n   * A validator for the arguments of this function.\n   *\n   * This is an object mapping argument names to validators constructed with\n   * {@link values.v}.\n   *\n   * ```js\n   * import { v } from \"convex/values\";\n   *\n   * const args = {\n   *   stringArg: v.string(),\n   *   optionalNumberArg: v.optional(v.number()),\n   * }\n   * ```\n   */\n  args: ArgsValidator;\n\n  /**\n   * The implementation of this function.\n   *\n   * This is a function that takes in the appropriate context and arguments\n   * and produces some result.\n   *\n   * @param ctx - The context object. This is one of {@link QueryCtx},\n   * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n   * @param args - The arguments object for this function. This will match\n   * the type defined by the argument validator.\n   * @returns\n   */\n  handler: (ctx: Ctx, args: ObjectType<ArgsValidator>) => Returns;\n}\n\n/**\n * There are multiple syntaxes for defining a Convex function:\n * ```\n *  - query(async (ctx, args) => {...})\n *  - query({ handler: async (ctx, args) => {...} })\n *  - query({ args: { a: v.string }, handler: async (ctx, args) => {...} } })\n *  - query({ args: { a: v.string }, returns: v.string(), handler: async (ctx, args) => {...} } })\n *```\n *\n * In each of these, we want to correctly infer the type for the arguments and\n * return value, preferring the type derived from a validator if it's provided.\n *\n * To avoid having a separate overload for each, which would show up in error messages,\n * we use the type params -- ArgsValidator, ReturnsValidator, ReturnValue, OneOrZeroArgs.\n *\n * The type for ReturnValue and OneOrZeroArgs are constrained by the type or ArgsValidator and\n * ReturnsValidator if they're present, and inferred from any explicit type annotations to the\n * arguments or return value of the function.\n *\n * Below are a few utility types to get the appropriate type constraints based on\n * an optional validator.\n *\n * Additional tricks:\n * - We use Validator | void instead of Validator | undefined because the latter does\n * not work with `strictNullChecks` since it's equivalent to just `Validator`.\n * - We use a tuple type of length 1 to avoid distribution over the union\n *  https://github.com/microsoft/TypeScript/issues/29368#issuecomment-453529532\n */\n\nexport type ReturnValueForOptionalValidator<\n  ReturnsValidator extends Validator<any, any, any> | PropertyValidators | void,\n> = [ReturnsValidator] extends [Validator<any, any, any>]\n  ? ValidatorTypeToReturnType<Infer<ReturnsValidator>>\n  : [ReturnsValidator] extends [PropertyValidators]\n    ? ValidatorTypeToReturnType<ObjectType<ReturnsValidator>>\n    : any;\n\nexport type ArgsArrayForOptionalValidator<\n  ArgsValidator extends GenericValidator | PropertyValidators | void,\n> = [ArgsValidator] extends [Validator<any, any, any>]\n  ? OneArgArray<Infer<ArgsValidator>>\n  : [ArgsValidator] extends [PropertyValidators]\n    ? OneArgArray<ObjectType<ArgsValidator>>\n    : ArgsArray;\n\nexport type DefaultArgsForOptionalValidator<\n  ArgsValidator extends GenericValidator | PropertyValidators | void,\n> = [ArgsValidator] extends [Validator<any, any, any>]\n  ? [Infer<ArgsValidator>]\n  : [ArgsValidator] extends [PropertyValidators]\n    ? [ObjectType<ArgsValidator>]\n    : OneArgArray;\n\n/**\n * Internal type helper used by Convex code generation.\n *\n * Used to give {@link mutationGeneric} a type specific to your data model.\n * @public\n */\nexport type MutationBuilder<\n  DataModel extends GenericDataModel,\n  Visibility extends FunctionVisibility,\n> = {\n  <\n    ArgsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnValue extends ReturnValueForOptionalValidator<ReturnsValidator> = any,\n    OneOrZeroArgs extends\n      ArgsArrayForOptionalValidator<ArgsValidator> = DefaultArgsForOptionalValidator<ArgsValidator>,\n  >(\n    mutation:\n      | {\n          /**\n           * Argument validation.\n           *\n           * Examples:\n           *\n           * ```\n           * args: {}\n           * args: { input: v.optional(v.number()) }\n           * args: { message: v.string(), author: v.id(\"authors\") }\n           * args: { messages: v.array(v.string()) }\n           * ```\n           */\n          args?: ArgsValidator;\n          /**\n           * The return value validator.\n           *\n           * Examples:\n           *\n           * ```\n           * returns: v.null()\n           * returns: v.string()\n           * returns: { message: v.string(), author: v.id(\"authors\") }\n           * returns: v.array(v.string())\n           * ```\n           */\n          returns?: ReturnsValidator;\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          handler: (\n            ctx: GenericMutationCtx<DataModel>,\n            ...args: OneOrZeroArgs\n          ) => ReturnValue;\n        }\n      | {\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          (\n            ctx: GenericMutationCtx<DataModel>,\n            ...args: OneOrZeroArgs\n          ): ReturnValue;\n        },\n  ): RegisteredMutation<\n    Visibility,\n    ArgsArrayToObject<OneOrZeroArgs>,\n    ReturnValue\n  >;\n};\n\n/**\n * Internal type helper used by Convex code generation.\n *\n * Used to give {@link mutationGeneric} a type specific to your data model.\n * @public\n */\nexport type MutationBuilderWithTable<\n  DataModel extends GenericDataModel,\n  Visibility extends FunctionVisibility,\n> = {\n  <\n    ArgsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnValue extends ReturnValueForOptionalValidator<ReturnsValidator> = any,\n    OneOrZeroArgs extends\n      ArgsArrayForOptionalValidator<ArgsValidator> = DefaultArgsForOptionalValidator<ArgsValidator>,\n  >(\n    mutation:\n      | {\n          /**\n           * Argument validation.\n           *\n           * Examples:\n           *\n           * ```\n           * args: {}\n           * args: { input: v.optional(v.number()) }\n           * args: { message: v.string(), author: v.id(\"authors\") }\n           * args: { messages: v.array(v.string()) }\n           * ```\n           */\n          args?: ArgsValidator;\n          /**\n           * The return value validator.\n           *\n           * Examples:\n           *\n           * ```\n           * returns: v.null()\n           * returns: v.string()\n           * returns: { message: v.string(), author: v.id(\"authors\") }\n           * returns: v.array(v.string())\n           * ```\n           */\n          returns?: ReturnsValidator;\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          handler: (\n            ctx: GenericMutationCtxWithTable<DataModel>,\n            ...args: OneOrZeroArgs\n          ) => ReturnValue;\n        }\n      | {\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          (\n            ctx: GenericMutationCtxWithTable<DataModel>,\n            ...args: OneOrZeroArgs\n          ): ReturnValue;\n        },\n  ): RegisteredMutation<\n    Visibility,\n    ArgsArrayToObject<OneOrZeroArgs>,\n    ReturnValue\n  >;\n};\n\n/**\n * Internal type helper used by Convex code generation.\n *\n * Used to give {@link queryGeneric} a type specific to your data model.\n * @public\n */\nexport type QueryBuilder<\n  DataModel extends GenericDataModel,\n  Visibility extends FunctionVisibility,\n> = {\n  <\n    ArgsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnValue extends ReturnValueForOptionalValidator<ReturnsValidator> = any,\n    OneOrZeroArgs extends\n      ArgsArrayForOptionalValidator<ArgsValidator> = DefaultArgsForOptionalValidator<ArgsValidator>,\n  >(\n    query:\n      | {\n          /**\n           * Argument validation.\n           *\n           * Examples:\n           *\n           * ```\n           * args: {}\n           * args: { input: v.optional(v.number()) }\n           * args: { message: v.string(), author: v.id(\"authors\") }\n           * args: { messages: v.array(v.string()) }\n           * ```\n           */\n          args?: ArgsValidator;\n          /**\n           * The return value validator.\n           *\n           * Examples:\n           *\n           * ```\n           * returns: v.null()\n           * returns: v.string()\n           * returns: { message: v.string(), author: v.id(\"authors\") }\n           * returns: v.array(v.string())\n           * ```\n           */\n          returns?: ReturnsValidator;\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          handler: (\n            ctx: GenericQueryCtx<DataModel>,\n            ...args: OneOrZeroArgs\n          ) => ReturnValue;\n        }\n      | {\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          (\n            ctx: GenericQueryCtx<DataModel>,\n            ...args: OneOrZeroArgs\n          ): ReturnValue;\n        },\n  ): RegisteredQuery<Visibility, ArgsArrayToObject<OneOrZeroArgs>, ReturnValue>;\n};\n\n/**\n * Internal type helper used by Convex code generation.\n *\n * Used to give {@link queryGeneric} a type specific to your data model.\n * @public\n */\nexport type QueryBuilderWithTable<\n  DataModel extends GenericDataModel,\n  Visibility extends FunctionVisibility,\n> = {\n  <\n    ArgsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnValue extends ReturnValueForOptionalValidator<ReturnsValidator> = any,\n    OneOrZeroArgs extends\n      ArgsArrayForOptionalValidator<ArgsValidator> = DefaultArgsForOptionalValidator<ArgsValidator>,\n  >(\n    query:\n      | {\n          /**\n           * Argument validation.\n           *\n           * Examples:\n           *\n           * ```\n           * args: {}\n           * args: { input: v.optional(v.number()) }\n           * args: { message: v.string(), author: v.id(\"authors\") }\n           * args: { messages: v.array(v.string()) }\n           * ```\n           */\n          args?: ArgsValidator;\n          /**\n           * The return value validator.\n           *\n           * Examples:\n           *\n           * ```\n           * returns: v.null()\n           * returns: v.string()\n           * returns: { message: v.string(), author: v.id(\"authors\") }\n           * returns: v.array(v.string())\n           * ```\n           */\n          returns?: ReturnsValidator;\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          handler: (\n            ctx: GenericQueryCtxWithTable<DataModel>,\n            ...args: OneOrZeroArgs\n          ) => ReturnValue;\n        }\n      | {\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          (\n            ctx: GenericQueryCtxWithTable<DataModel>,\n            ...args: OneOrZeroArgs\n          ): ReturnValue;\n        },\n  ): RegisteredQuery<Visibility, ArgsArrayToObject<OneOrZeroArgs>, ReturnValue>;\n};\n\n/**\n * Internal type helper used by Convex code generation.\n *\n * Used to give {@link actionGeneric} a type specific to your data model.\n * @public\n */\nexport type ActionBuilder<\n  DataModel extends GenericDataModel,\n  Visibility extends FunctionVisibility,\n> = {\n  <\n    ArgsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnsValidator extends\n      | PropertyValidators\n      | Validator<any, \"required\", any>\n      | void,\n    ReturnValue extends ReturnValueForOptionalValidator<ReturnsValidator> = any,\n    OneOrZeroArgs extends\n      ArgsArrayForOptionalValidator<ArgsValidator> = DefaultArgsForOptionalValidator<ArgsValidator>,\n  >(\n    func:\n      | {\n          /**\n           * Argument validation.\n           *\n           * Examples:\n           *\n           * ```\n           * args: {}\n           * args: { input: v.optional(v.number()) }\n           * args: { message: v.string(), author: v.id(\"authors\") }\n           * args: { messages: v.array(v.string()) }\n           * ```\n           *\n           */\n          args?: ArgsValidator;\n          /**\n           * The return value validator.\n           *\n           * Examples:\n           *\n           * ```\n           * returns: v.null()\n           * returns: v.string()\n           * returns: { message: v.string(), author: v.id(\"authors\") }\n           * returns: v.array(v.string())\n           * ```\n           */\n          returns?: ReturnsValidator;\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          handler: (\n            ctx: GenericActionCtx<DataModel>,\n            ...args: OneOrZeroArgs\n          ) => ReturnValue;\n        }\n      | {\n          /**\n           * The implementation of this function.\n           *\n           * This is a function that takes in the appropriate context and arguments\n           * and produces some result.\n           *\n           * @param ctx - The context object. This is one of {@link QueryCtx},\n           * {@link MutationCtx}, or {@link ActionCtx} depending on the function type.\n           * @param args - The arguments object for this function. This will match\n           * the type defined by the argument validator if provided.\n           * @returns\n           */\n          (\n            ctx: GenericActionCtx<DataModel>,\n            ...args: OneOrZeroArgs\n          ): ReturnValue;\n        },\n  ): RegisteredAction<\n    Visibility,\n    ArgsArrayToObject<OneOrZeroArgs>,\n    ReturnValue\n  >;\n};\n\n/**\n * Internal type helper used by Convex code generation.\n *\n * Used to give {@link httpActionGeneric} a type specific to your data model\n * and functions.\n * @public\n */\nexport type HttpActionBuilder = (\n  func: (ctx: GenericActionCtx<any>, request: Request) => Promise<Response>,\n) => PublicHttpAction;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}