{"version": 3, "sources": ["../../../src/server/scheduler.ts"], "sourcesContent": ["import { FunctionReference, OptionalRestArgs } from \"../server/api.js\";\nimport { Id } from \"../values/value.js\";\n\n/**\n * A {@link FunctionReference} that can be scheduled to run in the future.\n *\n * Schedulable functions are mutations and actions that are public or internal.\n *\n * @public\n */\nexport type SchedulableFunctionReference = FunctionReference<\n  \"mutation\" | \"action\",\n  \"public\" | \"internal\"\n>;\n\n/**\n * An interface to schedule Convex functions.\n *\n * You can schedule either mutations or actions. Mutations are guaranteed to execute\n * exactly once - they are automatically retried on transient errors and either execute\n * successfully or fail deterministically due to developer error in defining the\n * function. Actions execute at most once - they are not retried and might fail\n * due to transient errors.\n *\n * Consider using an {@link internalMutation} or {@link internalAction} to enforce that\n * these functions cannot be called directly from a Convex client.\n *\n * @public\n */\nexport interface Scheduler {\n  /**\n   * Schedule a function to execute after a delay.\n   *\n   * @param delayMs - Delay in milliseconds. Must be non-negative. If the delay\n   * is zero, the scheduled function will be due to execute immediately after the\n   * scheduling one completes.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - Arguments to call the scheduled functions with.\n   **/\n  runAfter<FuncRef extends SchedulableFunctionReference>(\n    delayMs: number,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ): Promise<Id<\"_scheduled_functions\">>;\n\n  /**\n   * Schedule a function to execute at a given timestamp.\n   *\n   * @param timestamp - A Date or a timestamp (milliseconds since the epoch).\n   * If the timestamp is in the past, the scheduled function will be due to\n   * execute immediately after the scheduling one completes. The timestamp can't\n   * be more than five years in the past or more than five years in the future.\n   * @param functionReference - A {@link FunctionReference} for the function\n   * to schedule.\n   * @param args - arguments to call the scheduled functions with.\n   **/\n  runAt<FuncRef extends SchedulableFunctionReference>(\n    timestamp: number | Date,\n    functionReference: FuncRef,\n    ...args: OptionalRestArgs<FuncRef>\n  ): Promise<Id<\"_scheduled_functions\">>;\n\n  /**\n   * Cancels a previously scheduled function if it has not started yet. If the\n   * scheduled function is already in progress, it will continue running but\n   * any new functions that it tries to schedule will be canceled.\n   *\n   * @param id\n   */\n  cancel(id: Id<\"_scheduled_functions\">): Promise<void>;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}