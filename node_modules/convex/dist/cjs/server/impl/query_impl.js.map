{"version": 3, "sources": ["../../../../src/server/impl/query_impl.ts"], "sourcesContent": ["import { Value, JSONValue, jsonToConvex } from \"../../values/index.js\";\nimport { PaginationResult, PaginationOptions } from \"../pagination.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  filterBuilderImpl,\n  serializeExpression,\n} from \"./filter_builder_impl.js\";\nimport { Query, QueryInitializer } from \"../query.js\";\nimport { ExpressionOrValue, FilterBuilder } from \"../filter_builder.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  IndexRangeBuilderImpl,\n  SerializedRangeExpression,\n} from \"./index_range_builder_impl.js\";\nimport {\n  SearchFilterBuilderImpl,\n  SerializedSearchFilter,\n} from \"./search_filter_builder_impl.js\";\nimport { validateArg, validateArgIsNonNegativeInteger } from \"./validate.js\";\nimport { version } from \"../../index.js\";\n\nconst MAX_QUERY_OPERATORS = 256;\n\ntype QueryOperator = { filter: JSONValue } | { limit: number };\ntype Source =\n  | { type: \"FullTableScan\"; tableName: string; order: \"asc\" | \"desc\" | null }\n  | {\n      type: \"IndexRange\";\n      indexName: string;\n      range: ReadonlyArray<SerializedRangeExpression>;\n      order: \"asc\" | \"desc\" | null;\n    }\n  | {\n      type: \"Search\";\n      indexName: string;\n      filters: ReadonlyArray<SerializedSearchFilter>;\n    };\n\ntype SerializedQuery = {\n  source: Source;\n  operators: Array<QueryOperator>;\n};\n\nexport class QueryInitializerImpl\n  implements QueryInitializer<GenericTableInfo>\n{\n  private tableName: string;\n\n  constructor(tableName: string) {\n    this.tableName = tableName;\n  }\n\n  withIndex(\n    indexName: string,\n    indexRange?: (q: IndexRangeBuilderImpl) => IndexRangeBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withIndex\", \"indexName\");\n    let rangeBuilder = IndexRangeBuilderImpl.new();\n    if (indexRange !== undefined) {\n      rangeBuilder = indexRange(rangeBuilder);\n    }\n    return new QueryImpl({\n      source: {\n        type: \"IndexRange\",\n        indexName: this.tableName + \".\" + indexName,\n        range: rangeBuilder.export(),\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  withSearchIndex(\n    indexName: string,\n    searchFilter: (q: SearchFilterBuilderImpl) => SearchFilterBuilderImpl,\n  ): QueryImpl {\n    validateArg(indexName, 1, \"withSearchIndex\", \"indexName\");\n    validateArg(searchFilter, 2, \"withSearchIndex\", \"searchFilter\");\n    const searchFilterBuilder = SearchFilterBuilderImpl.new();\n    return new QueryImpl({\n      source: {\n        type: \"Search\",\n        indexName: this.tableName + \".\" + indexName,\n        filters: searchFilter(searchFilterBuilder).export(),\n      },\n      operators: [],\n    });\n  }\n\n  fullTableScan(): QueryImpl {\n    return new QueryImpl({\n      source: {\n        type: \"FullTableScan\",\n        tableName: this.tableName,\n        order: null,\n      },\n      operators: [],\n    });\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    return this.fullTableScan().order(order);\n  }\n\n  // This is internal API and should not be exposed to developers yet.\n  async count(): Promise<number> {\n    const syscallJSON = await performAsyncSyscall(\"1.0/count\", {\n      table: this.tableName,\n    });\n    const syscallResult = jsonToConvex(syscallJSON) as number;\n    return syscallResult;\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ) {\n    return this.fullTableScan().filter(predicate);\n  }\n\n  limit(n: number) {\n    return this.fullTableScan().limit(n);\n  }\n\n  collect(): Promise<any[]> {\n    return this.fullTableScan().collect();\n  }\n\n  take(n: number): Promise<Array<any>> {\n    return this.fullTableScan().take(n);\n  }\n\n  paginate(paginationOpts: PaginationOptions): Promise<PaginationResult<any>> {\n    return this.fullTableScan().paginate(paginationOpts);\n  }\n\n  first(): Promise<any> {\n    return this.fullTableScan().first();\n  }\n\n  unique(): Promise<any> {\n    return this.fullTableScan().unique();\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    return this.fullTableScan()[Symbol.asyncIterator]();\n  }\n}\n\n/**\n * @param type Whether the query was consumed or closed.\n * @throws An error indicating the query has been closed.\n */\nfunction throwClosedError(type: \"closed\" | \"consumed\"): never {\n  throw new Error(\n    type === \"consumed\"\n      ? \"This query is closed and can't emit any more values.\"\n      : \"This query has been chained with another operator and can't be reused.\",\n  );\n}\n\nexport class QueryImpl implements Query<GenericTableInfo> {\n  private state:\n    | { type: \"preparing\"; query: SerializedQuery }\n    | { type: \"executing\"; queryId: number }\n    | { type: \"closed\" }\n    | { type: \"consumed\" };\n  private tableNameForErrorMessages: string;\n\n  constructor(query: SerializedQuery) {\n    this.state = { type: \"preparing\", query };\n    if (query.source.type === \"FullTableScan\") {\n      this.tableNameForErrorMessages = query.source.tableName;\n    } else {\n      this.tableNameForErrorMessages = query.source.indexName.split(\".\")[0];\n    }\n  }\n\n  private takeQuery(): SerializedQuery {\n    if (this.state.type !== \"preparing\") {\n      throw new Error(\n        \"A query can only be chained once and can't be chained after iteration begins.\",\n      );\n    }\n    const query = this.state.query;\n    this.state = { type: \"closed\" };\n    return query;\n  }\n\n  private startQuery(): number {\n    if (this.state.type === \"executing\") {\n      throw new Error(\"Iteration can only begin on a query once.\");\n    }\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    const query = this.state.query;\n    const { queryId } = performSyscall(\"1.0/queryStream\", { query, version });\n    this.state = { type: \"executing\", queryId };\n    return queryId;\n  }\n\n  private closeQuery() {\n    if (this.state.type === \"executing\") {\n      const queryId = this.state.queryId;\n      performSyscall(\"1.0/queryCleanup\", { queryId });\n    }\n    this.state = { type: \"consumed\" };\n  }\n\n  order(order: \"asc\" | \"desc\"): QueryImpl {\n    validateArg(order, 1, \"order\", \"order\");\n    const query = this.takeQuery();\n    if (query.source.type === \"Search\") {\n      throw new Error(\n        \"Search queries must always be in relevance order. Can not set order manually.\",\n      );\n    }\n    if (query.source.order !== null) {\n      throw new Error(\"Queries may only specify order at most once\");\n    }\n    query.source.order = order;\n    return new QueryImpl(query);\n  }\n\n  filter(\n    predicate: (\n      q: FilterBuilder<GenericTableInfo>,\n    ) => ExpressionOrValue<boolean>,\n  ): any {\n    validateArg(predicate, 1, \"filter\", \"predicate\");\n    const query = this.takeQuery();\n    if (query.operators.length >= MAX_QUERY_OPERATORS) {\n      throw new Error(\n        `Can't construct query with more than ${MAX_QUERY_OPERATORS} operators`,\n      );\n    }\n    query.operators.push({\n      filter: serializeExpression(predicate(filterBuilderImpl)),\n    });\n    return new QueryImpl(query);\n  }\n\n  limit(n: number): any {\n    validateArg(n, 1, \"limit\", \"n\");\n    const query = this.takeQuery();\n    query.operators.push({ limit: n });\n    return new QueryImpl(query);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterableIterator<any> {\n    this.startQuery();\n    return this;\n  }\n\n  async next(): Promise<IteratorResult<any>> {\n    if (this.state.type === \"closed\" || this.state.type === \"consumed\") {\n      throwClosedError(this.state.type);\n    }\n    // Allow calling `.next()` when the query is in \"preparing\" state to implicitly start the\n    // query. This allows the developer to call `.next()` on the query without having to use\n    // a `for await` statement.\n    const queryId =\n      this.state.type === \"preparing\" ? this.startQuery() : this.state.queryId;\n    const { value, done } = await performAsyncSyscall(\"1.0/queryStreamNext\", {\n      queryId,\n    });\n    if (done) {\n      this.closeQuery();\n    }\n    const convexValue = jsonToConvex(value);\n    return { value: convexValue, done };\n  }\n\n  return() {\n    this.closeQuery();\n    return Promise.resolve({ done: true, value: undefined });\n  }\n\n  async paginate(\n    paginationOpts: PaginationOptions,\n  ): Promise<PaginationResult<any>> {\n    validateArg(paginationOpts, 1, \"paginate\", \"options\");\n    if (\n      typeof paginationOpts?.numItems !== \"number\" ||\n      paginationOpts.numItems < 0\n    ) {\n      throw new Error(\n        `\\`options.numItems\\` must be a positive number. Received \\`${paginationOpts?.numItems}\\`.`,\n      );\n    }\n    const query = this.takeQuery();\n    const pageSize = paginationOpts.numItems;\n    const cursor = paginationOpts.cursor;\n    const endCursor = paginationOpts?.endCursor ?? null;\n    const maximumRowsRead = paginationOpts.maximumRowsRead ?? null;\n    const { page, isDone, continueCursor, splitCursor, pageStatus } =\n      await performAsyncSyscall(\"1.0/queryPage\", {\n        query,\n        cursor,\n        endCursor,\n        pageSize,\n        maximumRowsRead,\n        maximumBytesRead: paginationOpts.maximumBytesRead,\n        version,\n      });\n    return {\n      page: page.map((json: string) => jsonToConvex(json)),\n      isDone,\n      continueCursor,\n      splitCursor,\n      pageStatus,\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    const out: Value[] = [];\n    for await (const item of this) {\n      out.push(item);\n    }\n    return out;\n  }\n\n  async take(n: number): Promise<Array<any>> {\n    validateArg(n, 1, \"take\", \"n\");\n    validateArgIsNonNegativeInteger(n, 1, \"take\", \"n\");\n    return this.limit(n).collect();\n  }\n\n  async first(): Promise<any | null> {\n    const first_array = await this.take(1);\n    return first_array.length === 0 ? null : first_array[0];\n  }\n\n  async unique(): Promise<any | null> {\n    const first_two_array = await this.take(2);\n    if (first_two_array.length === 0) {\n      return null;\n    }\n    if (first_two_array.length === 2) {\n      throw new Error(`unique() query returned more than one result from table ${this.tableNameForErrorMessages}:\n [${first_two_array[0]._id}, ${first_two_array[1]._id}, ...]`);\n    }\n    return first_two_array[0];\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+C;AAE/C,qBAAoD;AACpD,iCAGO;AAIP,sCAGO;AACP,wCAGO;AACP,sBAA6D;AAC7D,eAAwB;AAExB,MAAM,sBAAsB;AAsBrB,MAAM,qBAEb;AAAA,EAGE,YAAY,WAAmB;AAF/B,wBAAQ;AAGN,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,UACE,WACA,YACW;AACX,qCAAY,WAAW,GAAG,aAAa,WAAW;AAClD,QAAI,eAAe,sDAAsB,IAAI;AAC7C,QAAI,eAAe,QAAW;AAC5B,qBAAe,WAAW,YAAY;AAAA,IACxC;AACA,WAAO,IAAI,UAAU;AAAA,MACnB,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,WAAW,KAAK,YAAY,MAAM;AAAA,QAClC,OAAO,aAAa,OAAO;AAAA,QAC3B,OAAO;AAAA,MACT;AAAA,MACA,WAAW,CAAC;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,gBACE,WACA,cACW;AACX,qCAAY,WAAW,GAAG,mBAAmB,WAAW;AACxD,qCAAY,cAAc,GAAG,mBAAmB,cAAc;AAC9D,UAAM,sBAAsB,0DAAwB,IAAI;AACxD,WAAO,IAAI,UAAU;AAAA,MACnB,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,WAAW,KAAK,YAAY,MAAM;AAAA,QAClC,SAAS,aAAa,mBAAmB,EAAE,OAAO;AAAA,MACpD;AAAA,MACA,WAAW,CAAC;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,gBAA2B;AACzB,WAAO,IAAI,UAAU;AAAA,MACnB,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,WAAW,KAAK;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,WAAW,CAAC;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,OAAkC;AACtC,WAAO,KAAK,cAAc,EAAE,MAAM,KAAK;AAAA,EACzC;AAAA;AAAA,EAGA,MAAM,QAAyB;AAC7B,UAAM,cAAc,UAAM,oCAAoB,aAAa;AAAA,MACzD,OAAO,KAAK;AAAA,IACd,CAAC;AACD,UAAM,oBAAgB,4BAAa,WAAW;AAC9C,WAAO;AAAA,EACT;AAAA,EAEA,OACE,WAGA;AACA,WAAO,KAAK,cAAc,EAAE,OAAO,SAAS;AAAA,EAC9C;AAAA,EAEA,MAAM,GAAW;AACf,WAAO,KAAK,cAAc,EAAE,MAAM,CAAC;AAAA,EACrC;AAAA,EAEA,UAA0B;AACxB,WAAO,KAAK,cAAc,EAAE,QAAQ;AAAA,EACtC;AAAA,EAEA,KAAK,GAAgC;AACnC,WAAO,KAAK,cAAc,EAAE,KAAK,CAAC;AAAA,EACpC;AAAA,EAEA,SAAS,gBAAmE;AAC1E,WAAO,KAAK,cAAc,EAAE,SAAS,cAAc;AAAA,EACrD;AAAA,EAEA,QAAsB;AACpB,WAAO,KAAK,cAAc,EAAE,MAAM;AAAA,EACpC;AAAA,EAEA,SAAuB;AACrB,WAAO,KAAK,cAAc,EAAE,OAAO;AAAA,EACrC;AAAA,EAEA,CAAC,OAAO,aAAa,IAAgC;AACnD,WAAO,KAAK,cAAc,EAAE,OAAO,aAAa,EAAE;AAAA,EACpD;AACF;AAMA,SAAS,iBAAiB,MAAoC;AAC5D,QAAM,IAAI;AAAA,IACR,SAAS,aACL,yDACA;AAAA,EACN;AACF;AAEO,MAAM,UAA6C;AAAA,EAQxD,YAAY,OAAwB;AAPpC,wBAAQ;AAKR,wBAAQ;AAGN,SAAK,QAAQ,EAAE,MAAM,aAAa,MAAM;AACxC,QAAI,MAAM,OAAO,SAAS,iBAAiB;AACzC,WAAK,4BAA4B,MAAM,OAAO;AAAA,IAChD,OAAO;AACL,WAAK,4BAA4B,MAAM,OAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAAA,IACtE;AAAA,EACF;AAAA,EAEQ,YAA6B;AACnC,QAAI,KAAK,MAAM,SAAS,aAAa;AACnC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,SAAK,QAAQ,EAAE,MAAM,SAAS;AAC9B,WAAO;AAAA,EACT;AAAA,EAEQ,aAAqB;AAC3B,QAAI,KAAK,MAAM,SAAS,aAAa;AACnC,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,QAAI,KAAK,MAAM,SAAS,YAAY,KAAK,MAAM,SAAS,YAAY;AAClE,uBAAiB,KAAK,MAAM,IAAI;AAAA,IAClC;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,EAAE,QAAQ,QAAI,+BAAe,mBAAmB,EAAE,OAAO,0BAAQ,CAAC;AACxE,SAAK,QAAQ,EAAE,MAAM,aAAa,QAAQ;AAC1C,WAAO;AAAA,EACT;AAAA,EAEQ,aAAa;AACnB,QAAI,KAAK,MAAM,SAAS,aAAa;AACnC,YAAM,UAAU,KAAK,MAAM;AAC3B,yCAAe,oBAAoB,EAAE,QAAQ,CAAC;AAAA,IAChD;AACA,SAAK,QAAQ,EAAE,MAAM,WAAW;AAAA,EAClC;AAAA,EAEA,MAAM,OAAkC;AACtC,qCAAY,OAAO,GAAG,SAAS,OAAO;AACtC,UAAM,QAAQ,KAAK,UAAU;AAC7B,QAAI,MAAM,OAAO,SAAS,UAAU;AAClC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,OAAO,UAAU,MAAM;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AACA,UAAM,OAAO,QAAQ;AACrB,WAAO,IAAI,UAAU,KAAK;AAAA,EAC5B;AAAA,EAEA,OACE,WAGK;AACL,qCAAY,WAAW,GAAG,UAAU,WAAW;AAC/C,UAAM,QAAQ,KAAK,UAAU;AAC7B,QAAI,MAAM,UAAU,UAAU,qBAAqB;AACjD,YAAM,IAAI;AAAA,QACR,wCAAwC,mBAAmB;AAAA,MAC7D;AAAA,IACF;AACA,UAAM,UAAU,KAAK;AAAA,MACnB,YAAQ,gDAAoB,UAAU,4CAAiB,CAAC;AAAA,IAC1D,CAAC;AACD,WAAO,IAAI,UAAU,KAAK;AAAA,EAC5B;AAAA,EAEA,MAAM,GAAgB;AACpB,qCAAY,GAAG,GAAG,SAAS,GAAG;AAC9B,UAAM,QAAQ,KAAK,UAAU;AAC7B,UAAM,UAAU,KAAK,EAAE,OAAO,EAAE,CAAC;AACjC,WAAO,IAAI,UAAU,KAAK;AAAA,EAC5B;AAAA,EAEA,CAAC,OAAO,aAAa,IAAgC;AACnD,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,OAAqC;AACzC,QAAI,KAAK,MAAM,SAAS,YAAY,KAAK,MAAM,SAAS,YAAY;AAClE,uBAAiB,KAAK,MAAM,IAAI;AAAA,IAClC;AAIA,UAAM,UACJ,KAAK,MAAM,SAAS,cAAc,KAAK,WAAW,IAAI,KAAK,MAAM;AACnE,UAAM,EAAE,OAAO,KAAK,IAAI,UAAM,oCAAoB,uBAAuB;AAAA,MACvE;AAAA,IACF,CAAC;AACD,QAAI,MAAM;AACR,WAAK,WAAW;AAAA,IAClB;AACA,UAAM,kBAAc,4BAAa,KAAK;AACtC,WAAO,EAAE,OAAO,aAAa,KAAK;AAAA,EACpC;AAAA,EAEA,SAAS;AACP,SAAK,WAAW;AAChB,WAAO,QAAQ,QAAQ,EAAE,MAAM,MAAM,OAAO,OAAU,CAAC;AAAA,EACzD;AAAA,EAEA,MAAM,SACJ,gBACgC;AAChC,qCAAY,gBAAgB,GAAG,YAAY,SAAS;AACpD,QACE,OAAO,gBAAgB,aAAa,YACpC,eAAe,WAAW,GAC1B;AACA,YAAM,IAAI;AAAA,QACR,8DAA8D,gBAAgB,QAAQ;AAAA,MACxF;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,UAAU;AAC7B,UAAM,WAAW,eAAe;AAChC,UAAM,SAAS,eAAe;AAC9B,UAAM,YAAY,gBAAgB,aAAa;AAC/C,UAAM,kBAAkB,eAAe,mBAAmB;AAC1D,UAAM,EAAE,MAAM,QAAQ,gBAAgB,aAAa,WAAW,IAC5D,UAAM,oCAAoB,iBAAiB;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkB,eAAe;AAAA,MACjC;AAAA,IACF,CAAC;AACH,WAAO;AAAA,MACL,MAAM,KAAK,IAAI,CAAC,aAAiB,4BAAa,IAAI,CAAC;AAAA,MACnD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,UAA+B;AACnC,UAAM,MAAe,CAAC;AACtB,qBAAiB,QAAQ,MAAM;AAC7B,UAAI,KAAK,IAAI;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,KAAK,GAAgC;AACzC,qCAAY,GAAG,GAAG,QAAQ,GAAG;AAC7B,yDAAgC,GAAG,GAAG,QAAQ,GAAG;AACjD,WAAO,KAAK,MAAM,CAAC,EAAE,QAAQ;AAAA,EAC/B;AAAA,EAEA,MAAM,QAA6B;AACjC,UAAM,cAAc,MAAM,KAAK,KAAK,CAAC;AACrC,WAAO,YAAY,WAAW,IAAI,OAAO,YAAY,CAAC;AAAA,EACxD;AAAA,EAEA,MAAM,SAA8B;AAClC,UAAM,kBAAkB,MAAM,KAAK,KAAK,CAAC;AACzC,QAAI,gBAAgB,WAAW,GAAG;AAChC,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,WAAW,GAAG;AAChC,YAAM,IAAI,MAAM,2DAA2D,KAAK,yBAAyB;AAAA,IAC3G,gBAAgB,CAAC,EAAE,GAAG,KAAK,gBAAgB,CAAC,EAAE,GAAG,QAAQ;AAAA,IACzD;AACA,WAAO,gBAAgB,CAAC;AAAA,EAC1B;AACF;", "names": []}