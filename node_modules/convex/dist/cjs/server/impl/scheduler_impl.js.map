{"version": 3, "sources": ["../../../../src/server/impl/scheduler_impl.ts"], "sourcesContent": ["import { convexToJson, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { SchedulableFunctionReference, Scheduler } from \"../scheduler.js\";\nimport { Id } from \"../../values/value.js\";\nimport { validateArg } from \"./validate.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nexport function setupMutationScheduler(): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAfterSyscallArgs(delayMs, functionReference, args);\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = runAtSyscallArgs(\n        ms_since_epoch_or_date,\n        functionReference,\n        args,\n      );\n      return await performAsyncSyscall(\"1.0/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const args = { id: convexToJson(id) };\n      await performAsyncSyscall(\"1.0/cancel_job\", args);\n    },\n  };\n}\n\nexport function setupActionScheduler(requestId: string): Scheduler {\n  return {\n    runAfter: async (\n      delayMs: number,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAfterSyscallArgs(delayMs, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    runAt: async (\n      ms_since_epoch_or_date: number | Date,\n      functionReference: SchedulableFunctionReference,\n      args?: Record<string, Value>,\n    ) => {\n      const syscallArgs = {\n        requestId,\n        ...runAtSyscallArgs(ms_since_epoch_or_date, functionReference, args),\n      };\n      return await performAsyncSyscall(\"1.0/actions/schedule\", syscallArgs);\n    },\n    cancel: async (id: Id<\"_scheduled_functions\">) => {\n      validateArg(id, 1, \"cancel\", \"id\");\n      const syscallArgs = { id: convexToJson(id) };\n      return await performAsyncSyscall(\"1.0/actions/cancel_job\", syscallArgs);\n    },\n  };\n}\n\nfunction runAfterSyscallArgs(\n  delayMs: number,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  if (typeof delayMs !== \"number\") {\n    throw new Error(\"`delayMs` must be a number\");\n  }\n  if (!isFinite(delayMs)) {\n    throw new Error(\"`delayMs` must be a finite number\");\n  }\n  if (delayMs < 0) {\n    throw new Error(\"`delayMs` must be non-negative\");\n  }\n  const functionArgs = parseArgs(args);\n  const address = getFunctionAddress(functionReference);\n  // Note the syscall expects a unix timestamp, measured in seconds.\n  const ts = (Date.now() + delayMs) / 1000.0;\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n\nfunction runAtSyscallArgs(\n  ms_since_epoch_or_date: number | Date,\n  functionReference: SchedulableFunctionReference,\n  args?: Record<string, Value>,\n) {\n  let ts;\n  if (ms_since_epoch_or_date instanceof Date) {\n    ts = ms_since_epoch_or_date.valueOf() / 1000.0;\n  } else if (typeof ms_since_epoch_or_date === \"number\") {\n    // The timestamp the developer passes is in milliseconds, while the syscall\n    // accepts seconds since the epoch.\n    ts = ms_since_epoch_or_date / 1000;\n  } else {\n    throw new Error(\"The invoke time must a Date or a timestamp\");\n  }\n  const address = getFunctionAddress(functionReference);\n  const functionArgs = parseArgs(args);\n  return {\n    ...address,\n    ts,\n    args: convexToJson(functionArgs),\n    version,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAoC;AACpC,eAAwB;AACxB,qBAAoC;AACpC,oBAA0B;AAG1B,sBAA4B;AAC5B,mBAAmC;AAE5B,SAAS,yBAAoC;AAClD,SAAO;AAAA,IACL,UAAU,OACR,SACA,mBACA,SACG;AACH,YAAM,cAAc,oBAAoB,SAAS,mBAAmB,IAAI;AACxE,aAAO,UAAM,oCAAoB,gBAAgB,WAAW;AAAA,IAC9D;AAAA,IACA,OAAO,OACL,wBACA,mBACA,SACG;AACH,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,UAAM,oCAAoB,gBAAgB,WAAW;AAAA,IAC9D;AAAA,IACA,QAAQ,OAAO,OAAmC;AAChD,uCAAY,IAAI,GAAG,UAAU,IAAI;AACjC,YAAM,OAAO,EAAE,QAAI,4BAAa,EAAE,EAAE;AACpC,gBAAM,oCAAoB,kBAAkB,IAAI;AAAA,IAClD;AAAA,EACF;AACF;AAEO,SAAS,qBAAqB,WAA8B;AACjE,SAAO;AAAA,IACL,UAAU,OACR,SACA,mBACA,SACG;AACH,YAAM,cAAc;AAAA,QAClB;AAAA,QACA,GAAG,oBAAoB,SAAS,mBAAmB,IAAI;AAAA,MACzD;AACA,aAAO,UAAM,oCAAoB,wBAAwB,WAAW;AAAA,IACtE;AAAA,IACA,OAAO,OACL,wBACA,mBACA,SACG;AACH,YAAM,cAAc;AAAA,QAClB;AAAA,QACA,GAAG,iBAAiB,wBAAwB,mBAAmB,IAAI;AAAA,MACrE;AACA,aAAO,UAAM,oCAAoB,wBAAwB,WAAW;AAAA,IACtE;AAAA,IACA,QAAQ,OAAO,OAAmC;AAChD,uCAAY,IAAI,GAAG,UAAU,IAAI;AACjC,YAAM,cAAc,EAAE,QAAI,4BAAa,EAAE,EAAE;AAC3C,aAAO,UAAM,oCAAoB,0BAA0B,WAAW;AAAA,IACxE;AAAA,EACF;AACF;AAEA,SAAS,oBACP,SACA,mBACA,MACA;AACA,MAAI,OAAO,YAAY,UAAU;AAC/B,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACA,MAAI,CAAC,SAAS,OAAO,GAAG;AACtB,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACrD;AACA,MAAI,UAAU,GAAG;AACf,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AACA,QAAM,mBAAe,yBAAU,IAAI;AACnC,QAAM,cAAU,iCAAmB,iBAAiB;AAEpD,QAAM,MAAM,KAAK,IAAI,IAAI,WAAW;AACpC,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA,UAAM,4BAAa,YAAY;AAAA,IAC/B;AAAA,EACF;AACF;AAEA,SAAS,iBACP,wBACA,mBACA,MACA;AACA,MAAI;AACJ,MAAI,kCAAkC,MAAM;AAC1C,SAAK,uBAAuB,QAAQ,IAAI;AAAA,EAC1C,WAAW,OAAO,2BAA2B,UAAU;AAGrD,SAAK,yBAAyB;AAAA,EAChC,OAAO;AACL,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC9D;AACA,QAAM,cAAU,iCAAmB,iBAAiB;AACpD,QAAM,mBAAe,yBAAU,IAAI;AACnC,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA,UAAM,4BAAa,YAAY;AAAA,IAC/B;AAAA,EACF;AACF;", "names": []}