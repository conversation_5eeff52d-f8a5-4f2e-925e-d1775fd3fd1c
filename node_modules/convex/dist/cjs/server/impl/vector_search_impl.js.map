{"version": 3, "sources": ["../../../../src/server/impl/vector_search_impl.ts"], "sourcesContent": ["import { J<PERSON>NValue } from \"../../values/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { version } from \"../../index.js\";\nimport {\n  FilterExpression,\n  VectorFilterBuilder,\n  VectorSearch,\n  VectorSearchQuery,\n} from \"../vector_search.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDataModel,\n  GenericDocument,\n  GenericTableInfo,\n  GenericVectorIndexConfig,\n} from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { Value, convexOrUndefinedToJson } from \"../../values/value.js\";\n\nexport function setupActionVectorSearch(\n  requestId: string,\n): VectorSearch<GenericDataModel, string, string> {\n  return async (\n    tableName: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) => {\n    validateArg(tableName, 1, \"vectorSearch\", \"tableName\");\n    validateArg(indexName, 2, \"vectorSearch\", \"indexName\");\n    validateArg(query, 3, \"vectorSearch\", \"query\");\n    if (\n      !query.vector ||\n      !Array.isArray(query.vector) ||\n      query.vector.length === 0\n    ) {\n      throw Error(\"`vector` must be a non-empty Array in vectorSearch\");\n    }\n\n    return await new VectorQueryImpl(\n      requestId,\n      tableName + \".\" + indexName,\n      query,\n    ).collect();\n  };\n}\n\nexport class VectorQueryImpl {\n  private requestId: string;\n  private state:\n    | { type: \"preparing\"; query: SerializedVectorQuery }\n    | { type: \"consumed\" };\n\n  constructor(\n    requestId: string,\n    indexName: string,\n    query: VectorSearchQuery<GenericTableInfo, string>,\n  ) {\n    this.requestId = requestId;\n    const filters = query.filter\n      ? serializeExpression(query.filter(filterBuilderImpl))\n      : null;\n\n    this.state = {\n      type: \"preparing\",\n      query: {\n        indexName,\n        limit: query.limit,\n        vector: query.vector,\n        expressions: filters,\n      },\n    };\n  }\n\n  async collect(): Promise<Array<any>> {\n    if (this.state.type === \"consumed\") {\n      throw new Error(\"This query is closed and can't emit any more values.\");\n    }\n    const query = this.state.query;\n    this.state = { type: \"consumed\" };\n\n    const { results } = await performAsyncSyscall(\"1.0/actions/vectorSearch\", {\n      requestId: this.requestId,\n      version,\n      query,\n    });\n    return results;\n  }\n}\n\ntype SerializedVectorQuery = {\n  indexName: string;\n  limit?: number;\n  vector: Array<number>;\n  expressions: JSONValue;\n};\n\ntype ExpressionOrValue<T extends Value | undefined> = FilterExpression<T> | T;\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends FilterExpression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: VectorFilterBuilder<\n  GenericDocument,\n  GenericVectorIndexConfig\n> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<FieldName extends GenericVectorIndexConfig[\"filterFields\"]>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): FilterExpression<boolean> {\n    if (typeof fieldName !== \"string\") {\n      throw new Error(\"The first argument to `q.eq` must be a field name.\");\n    }\n    return new ExpressionImpl({\n      $eq: [\n        serializeExpression(new ExpressionImpl({ $field: fieldName })),\n        serializeExpression(value),\n      ],\n    });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): FilterExpression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAoC;AACpC,eAAwB;AACxB,2BAKO;AAQP,sBAA4B;AAC5B,mBAA+C;AAExC,SAAS,wBACd,WACgD;AAChD,SAAO,OACL,WACA,WACA,UACG;AACH,qCAAY,WAAW,GAAG,gBAAgB,WAAW;AACrD,qCAAY,WAAW,GAAG,gBAAgB,WAAW;AACrD,qCAAY,OAAO,GAAG,gBAAgB,OAAO;AAC7C,QACE,CAAC,MAAM,UACP,CAAC,MAAM,QAAQ,MAAM,MAAM,KAC3B,MAAM,OAAO,WAAW,GACxB;AACA,YAAM,MAAM,oDAAoD;AAAA,IAClE;AAEA,WAAO,MAAM,IAAI;AAAA,MACf;AAAA,MACA,YAAY,MAAM;AAAA,MAClB;AAAA,IACF,EAAE,QAAQ;AAAA,EACZ;AACF;AAEO,MAAM,gBAAgB;AAAA,EAM3B,YACE,WACA,WACA,OACA;AATF,wBAAQ;AACR,wBAAQ;AASN,SAAK,YAAY;AACjB,UAAM,UAAU,MAAM,SAClB,oBAAoB,MAAM,OAAO,iBAAiB,CAAC,IACnD;AAEJ,SAAK,QAAQ;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,QACL;AAAA,QACA,OAAO,MAAM;AAAA,QACb,QAAQ,MAAM;AAAA,QACd,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,UAA+B;AACnC,QAAI,KAAK,MAAM,SAAS,YAAY;AAClC,YAAM,IAAI,MAAM,sDAAsD;AAAA,IACxE;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,SAAK,QAAQ,EAAE,MAAM,WAAW;AAEhC,UAAM,EAAE,QAAQ,IAAI,UAAM,oCAAoB,4BAA4B;AAAA,MACxE,WAAW,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAaO,MAAM,uBAAuB,sCAAsB;AAAA,EAExD,YAAY,OAAkB;AAC5B,UAAM;AAFR,wBAAQ;AAGN,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,YAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AACF;AAEO,SAAS,oBACd,MACW;AACX,MAAI,gBAAgB,gBAAgB;AAClC,WAAO,KAAK,UAAU;AAAA,EACxB,OAAO;AAGL,WAAO,EAAE,cAAU,sCAAwB,IAAyB,EAAE;AAAA,EACxE;AACF;AAEO,MAAM,oBAGT;AAAA;AAAA,EAGF,GACE,WACA,OAC2B;AAC3B,QAAI,OAAO,cAAc,UAAU;AACjC,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AACA,WAAO,IAAI,eAAe;AAAA,MACxB,KAAK;AAAA,QACH,oBAAoB,IAAI,eAAe,EAAE,QAAQ,UAAU,CAAC,CAAC;AAAA,QAC7D,oBAAoB,KAAK;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAIA,MAAM,OAAqE;AACzE,WAAO,IAAI,eAAe,EAAE,KAAK,MAAM,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACnE;AACF;", "names": []}