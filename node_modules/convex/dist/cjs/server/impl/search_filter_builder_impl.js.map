{"version": 3, "sources": ["../../../../src/server/impl/search_filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, convexOrUndefinedToJson } from \"../../values/value.js\";\nimport {\n  FieldTypeFromFieldPath,\n  GenericDocument,\n  GenericSearchIndexConfig,\n} from \"../data_model.js\";\nimport {\n  SearchFilter,\n  SearchFilterBuilder,\n  SearchFilterFinalizer,\n} from \"../search_filter_builder.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport type SerializedSearchFilter =\n  | {\n      type: \"Search\";\n      fieldPath: string;\n      value: string;\n    }\n  | {\n      type: \"Eq\";\n      fieldPath: string;\n      value: JSONValue;\n    };\n\nexport class SearchFilterBuilderImpl\n  extends SearchFilter\n  implements\n    SearchFilterBuilder<GenericDocument, GenericSearchIndexConfig>,\n    SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig>\n{\n  private filters: ReadonlyArray<SerializedSearchFilter>;\n  private isConsumed: boolean;\n  private constructor(filters: ReadonlyArray<SerializedSearchFilter>) {\n    super();\n    this.filters = filters;\n    this.isConsumed = false;\n  }\n\n  static new(): SearchFilterBuilderImpl {\n    return new SearchFilterBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  search(\n    fieldName: string,\n    query: string,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"search\", \"fieldName\");\n    validateArg(query, 2, \"search\", \"query\");\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Search\",\n        fieldPath: fieldName,\n        value: query,\n      }),\n    );\n  }\n  eq<FieldName extends string>(\n    fieldName: FieldName,\n    value: FieldTypeFromFieldPath<GenericDocument, FieldName>,\n  ): SearchFilterFinalizer<GenericDocument, GenericSearchIndexConfig> {\n    validateArg(fieldName, 1, \"eq\", \"fieldName\");\n    // when `undefined` is passed explicitly, it is allowed.\n    if (arguments.length !== 2) {\n      validateArg(value, 2, \"search\", \"value\");\n    }\n    this.consume();\n    return new SearchFilterBuilderImpl(\n      this.filters.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.filters;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAmD;AAMnD,mCAIO;AACP,sBAA4B;AAcrB,MAAM,gCACH,0CAIV;AAAA,EAGU,YAAY,SAAgD;AAClE,UAAM;AAHR,wBAAQ;AACR,wBAAQ;AAGN,SAAK,UAAU;AACf,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,OAAO,MAA+B;AACpC,WAAO,IAAI,wBAAwB,CAAC,CAAC;AAAA,EACvC;AAAA,EAEQ,UAAU;AAChB,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,OACE,WACA,OACkE;AAClE,qCAAY,WAAW,GAAG,UAAU,WAAW;AAC/C,qCAAY,OAAO,GAAG,UAAU,OAAO;AACvC,SAAK,QAAQ;AACb,WAAO,IAAI;AAAA,MACT,KAAK,QAAQ,OAAO;AAAA,QAClB,MAAM;AAAA,QACN,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,GACE,WACA,OACkE;AAClE,qCAAY,WAAW,GAAG,MAAM,WAAW;AAE3C,QAAI,UAAU,WAAW,GAAG;AAC1B,uCAAY,OAAO,GAAG,UAAU,OAAO;AAAA,IACzC;AACA,SAAK,QAAQ;AACb,WAAO,IAAI;AAAA,MACT,KAAK,QAAQ,OAAO;AAAA,QAClB,MAAM;AAAA,QACN,WAAW;AAAA,QACX,WAAO,sCAAwB,KAAK;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,SAAS;AACP,SAAK,QAAQ;AACb,WAAO,KAAK;AAAA,EACd;AACF;", "names": []}