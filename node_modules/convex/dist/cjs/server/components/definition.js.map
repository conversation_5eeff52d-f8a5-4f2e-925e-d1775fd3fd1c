{"version": 3, "sources": ["../../../../src/server/components/definition.ts"], "sourcesContent": ["// These reflect server types.\nexport type ComponentDefinitionExport = {\n  name: string;\n  // how will we figure this out?\n  path: string;\n  definitionType: {\n    type: \"childComponent\";\n    name: string;\n    args: [string, { type: \"value\"; value: string }][];\n  };\n  childComponents: [];\n  exports: { type: \"branch\"; branch: [] };\n};\n\n// These reflect server types.\n// type ComponentDefinitionType\nexport type ComponentDefinitionType = {\n  type: \"childComponent\";\n  name: string;\n  args: [string, { type: \"value\"; value: string }][];\n};\nexport type AppDefinitionType = { type: \"app\" };\n\ntype ComponentInstantiation = {\n  name: string;\n  // This is a ComponentPath.\n  path: string;\n  args: [string, { type: \"value\"; value: string }][];\n};\n\nexport type HttpMount = string;\n\ntype ComponentExport =\n  | { type: \"branch\"; branch: [string, ComponentExport][] }\n  | { type: \"leaf\"; leaf: string };\n\n// The type expected from the internal .export()\n// method of a component or app definition.\nexport type ComponentDefinitionAnalysis = {\n  name: string;\n  definitionType: ComponentDefinitionType;\n  childComponents: ComponentInstantiation[];\n  httpMounts: Record<string, HttpMount>;\n  exports: ComponentExport;\n};\nexport type AppDefinitionAnalysis = {\n  definitionType: AppDefinitionType;\n  childComponents: ComponentInstantiation[];\n  httpMounts: Record<string, HttpMount>;\n  exports: ComponentExport;\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}