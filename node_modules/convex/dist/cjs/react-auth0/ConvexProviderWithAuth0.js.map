{"version": 3, "sources": ["../../../src/react-auth0/ConvexProviderWithAuth0.tsx"], "sourcesContent": ["import { useAuth0 } from \"@auth0/auth0-react\";\nimport React from \"react\";\n\nimport { ReactNode, useCallback, useMemo } from \"react\";\nimport { AuthTokenFetcher } from \"../browser/sync/client.js\";\nimport { ConvexProviderWithAuth } from \"../react/ConvexAuthState.js\";\n\n// Until we can import from our own entry points (requires TypeScript 4.7),\n// just describe the interface enough to help users pass the right type.\ntype IConvexReactClient = {\n  setAuth(fetchToken: AuthTokenFetcher): void;\n  clearAuth(): void;\n};\n\n/**\n * A wrapper React component which provides a {@link react.ConvexReactClient}\n * authenticated with Auth0.\n *\n * It must be wrapped by a configured `Auth0Provider` from `@auth0/auth0-react`.\n *\n * See [Convex Auth0](https://docs.convex.dev/auth/auth0) on how to set up\n * Convex with Auth0.\n *\n * @public\n */\nexport function ConvexProviderWithAuth0({\n  children,\n  client,\n}: {\n  children: ReactNode;\n  client: IConvexReactClient;\n}) {\n  return (\n    <ConvexProviderWithAuth client={client} useAuth={useAuthFromAuth0}>\n      {children}\n    </ConvexProviderWithAuth>\n  );\n}\n\nfunction useAuthFromAuth0() {\n  const { isLoading, isAuthenticated, getAccessTokenSilently } = useAuth0();\n  const fetchAccessToken = useCallback(\n    async ({ forceRefreshToken }: { forceRefreshToken: boolean }) => {\n      try {\n        const response = await getAccessTokenSilently({\n          detailedResponse: true,\n          cacheMode: forceRefreshToken ? \"off\" : \"on\",\n        });\n        return response.id_token as string;\n      } catch {\n        return null;\n      }\n    },\n    [getAccessTokenSilently],\n  );\n  return useMemo(\n    () => ({ isLoading, isAuthenticated, fetchAccessToken }),\n    [isLoading, isAuthenticated, fetchAccessToken],\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAyB;AACzB,mBAAkB;AAElB,IAAAA,gBAAgD;AAEhD,6BAAuC;AAoBhC,SAAS,wBAAwB;AAAA,EACtC;AAAA,EACA;AACF,GAGG;AACD,SACE,6BAAAC,QAAA,cAAC,iDAAuB,QAAgB,SAAS,oBAC9C,QACH;AAEJ;AAEA,SAAS,mBAAmB;AAC1B,QAAM,EAAE,WAAW,iBAAiB,uBAAuB,QAAI,6BAAS;AACxE,QAAM,uBAAmB;AAAA,IACvB,OAAO,EAAE,kBAAkB,MAAsC;AAC/D,UAAI;AACF,cAAM,WAAW,MAAM,uBAAuB;AAAA,UAC5C,kBAAkB;AAAA,UAClB,WAAW,oBAAoB,QAAQ;AAAA,QACzC,CAAC;AACD,eAAO,SAAS;AAAA,MAClB,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,CAAC,sBAAsB;AAAA,EACzB;AACA,aAAO;AAAA,IACL,OAAO,EAAE,WAAW,iBAAiB,iBAAiB;AAAA,IACtD,CAAC,WAAW,iBAAiB,gBAAgB;AAAA,EAC/C;AACF;", "names": ["import_react", "React"]}