{"version": 3, "sources": ["../../../src/react/ConvexAuthState.tsx"], "sourcesContent": ["import React, {\n  createContext,\n  ReactNode,\n  useContext,\n  useEffect,\n  useState,\n} from \"react\";\nimport { AuthTokenFetcher } from \"../browser/sync/client.js\";\nimport { ConvexProvider } from \"./client.js\";\n\n// Until we can import from our own entry points (requires TypeScript 4.7),\n// just describe the interface enough to help users pass the right type.\ntype IConvexReactClient = {\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ): void;\n  clearAuth(): void;\n};\n\n/**\n * Type representing the state of an auth integration with Convex.\n *\n * @public\n */\nexport type ConvexAuthState = {\n  isLoading: boolean;\n  isAuthenticated: boolean;\n};\n\nconst ConvexAuthContext = createContext<ConvexAuthState>(undefined as any);\n\n/**\n * Get the {@link ConvexAuthState} within a React component.\n *\n * This relies on a Convex auth integration provider being above in the React\n * component tree.\n *\n * @returns The current {@link ConvexAuthState}.\n *\n * @public\n */\nexport function useConvexAuth(): {\n  isLoading: boolean;\n  isAuthenticated: boolean;\n} {\n  const authContext = useContext(ConvexAuthContext);\n  if (authContext === undefined) {\n    throw new Error(\n      \"Could not find `ConvexProviderWithAuth` (or `ConvexProviderWithClerk` \" +\n        \"or `ConvexProviderWithAuth0`) \" +\n        \"as an ancestor component. This component may be missing, or you \" +\n        \"might have two instances of the `convex/react` module loaded in your \" +\n        \"project.\",\n    );\n  }\n  return authContext;\n}\n\n/**\n * A replacement for {@link ConvexProvider} which additionally provides\n * {@link ConvexAuthState} to descendants of this component.\n *\n * Use this to integrate any auth provider with Convex. The `useAuth` prop\n * should be a React hook that returns the provider's authentication state\n * and a function to fetch a JWT access token.\n *\n * If the `useAuth` prop function updates causing a rerender then auth state\n * wil transition to loading and the `fetchAccessToken()` function called again.\n *\n * See [Custom Auth Integration](https://docs.convex.dev/auth/advanced/custom-auth) for more information.\n *\n * @public\n */\nexport function ConvexProviderWithAuth({\n  children,\n  client,\n  useAuth,\n}: {\n  children?: ReactNode;\n  client: IConvexReactClient;\n  useAuth: () => {\n    isLoading: boolean;\n    isAuthenticated: boolean;\n    fetchAccessToken: (args: {\n      forceRefreshToken: boolean;\n    }) => Promise<string | null>;\n  };\n}) {\n  const {\n    isLoading: authProviderLoading,\n    isAuthenticated: authProviderAuthenticated,\n    fetchAccessToken,\n  } = useAuth();\n  const [isConvexAuthenticated, setIsConvexAuthenticated] = useState<\n    boolean | null\n  >(null);\n\n  // If the useAuth went back to the authProviderLoading state (which is unusual but possible)\n  // reset the Convex auth state to null so that we can correctly\n  // transition the state from \"loading\" to \"authenticated\"\n  // without going through \"unauthenticated\".\n  if (authProviderLoading && isConvexAuthenticated !== null) {\n    setIsConvexAuthenticated(null);\n  }\n\n  // If the useAuth goes to not authenticated then isConvexAuthenticated should reflect that.\n  if (\n    !authProviderLoading &&\n    !authProviderAuthenticated &&\n    isConvexAuthenticated !== false\n  ) {\n    setIsConvexAuthenticated(false);\n  }\n\n  return (\n    <ConvexAuthContext.Provider\n      value={{\n        isLoading: isConvexAuthenticated === null,\n        isAuthenticated:\n          authProviderAuthenticated && (isConvexAuthenticated ?? false),\n      }}\n    >\n      <ConvexAuthStateFirstEffect\n        authProviderAuthenticated={authProviderAuthenticated}\n        fetchAccessToken={fetchAccessToken}\n        authProviderLoading={authProviderLoading}\n        client={client}\n        setIsConvexAuthenticated={setIsConvexAuthenticated}\n      />\n      <ConvexProvider client={client as any}>{children}</ConvexProvider>\n      <ConvexAuthStateLastEffect\n        authProviderAuthenticated={authProviderAuthenticated}\n        fetchAccessToken={fetchAccessToken}\n        authProviderLoading={authProviderLoading}\n        client={client}\n        setIsConvexAuthenticated={setIsConvexAuthenticated}\n      />\n    </ConvexAuthContext.Provider>\n  );\n}\n\n// First child ensures we `setAuth` before\n// other child components subscribe to queries via `useEffect`.\nfunction ConvexAuthStateFirstEffect({\n  authProviderAuthenticated,\n  fetchAccessToken,\n  authProviderLoading,\n  client,\n  setIsConvexAuthenticated,\n}: {\n  authProviderAuthenticated: boolean;\n  fetchAccessToken: (args: {\n    forceRefreshToken: boolean;\n  }) => Promise<string | null>;\n  authProviderLoading: boolean;\n  client: IConvexReactClient;\n  setIsConvexAuthenticated: React.Dispatch<\n    React.SetStateAction<boolean | null>\n  >;\n}) {\n  useEffect(() => {\n    let isThisEffectRelevant = true;\n    if (authProviderAuthenticated) {\n      client.setAuth(fetchAccessToken, (backendReportsIsAuthenticated) => {\n        if (isThisEffectRelevant) {\n          setIsConvexAuthenticated(() => backendReportsIsAuthenticated);\n        }\n      });\n      return () => {\n        isThisEffectRelevant = false;\n\n        // If unmounting or something changed before we finished fetching the token\n        // we shouldn't transition to a loaded state.\n        setIsConvexAuthenticated((isConvexAuthenticated) =>\n          isConvexAuthenticated ? false : null,\n        );\n      };\n    }\n  }, [\n    authProviderAuthenticated,\n    fetchAccessToken,\n    authProviderLoading,\n    client,\n    setIsConvexAuthenticated,\n  ]);\n  return null;\n}\n\n// Last child ensures we `clearAuth` last,\n// so that queries from unmounted sibling components\n// unsubscribe first and don't rerun without auth on the server\nfunction ConvexAuthStateLastEffect({\n  authProviderAuthenticated,\n  fetchAccessToken,\n  authProviderLoading,\n  client,\n  setIsConvexAuthenticated,\n}: {\n  authProviderAuthenticated: boolean;\n  fetchAccessToken: (args: {\n    forceRefreshToken: boolean;\n  }) => Promise<string | null>;\n  authProviderLoading: boolean;\n  client: IConvexReactClient;\n  setIsConvexAuthenticated: React.Dispatch<\n    React.SetStateAction<boolean | null>\n  >;\n}) {\n  useEffect(() => {\n    // If rendered with authProviderAuthenticated=true then clear that auth on in cleanup.\n    if (authProviderAuthenticated) {\n      return () => {\n        client.clearAuth();\n        // Set state back to loading in case this is a transition from one\n        // fetchToken function to another which signals a new auth context,\n        // e.g. a new orgId from Clerk. Auth context changes like this\n        // return isAuthenticated: true from useAuth() but if\n        // useAuth reports isAuthenticated: false on the next render\n        // then this null value will be overridden to false.\n        setIsConvexAuthenticated(() => null);\n      };\n    }\n  }, [\n    authProviderAuthenticated,\n    fetchAccessToken,\n    authProviderLoading,\n    client,\n    setIsConvexAuthenticated,\n  ]);\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAMO;AAEP,IAAAA,iBAA+B;AAsB/B,MAAM,wBAAoB,4BAA+B,MAAgB;AAYlE,SAAS,gBAGd;AACA,QAAM,kBAAc,yBAAW,iBAAiB;AAChD,MAAI,gBAAgB,QAAW;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IAKF;AAAA,EACF;AACA,SAAO;AACT;AAiBO,SAAS,uBAAuB;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AACF,GAUG;AACD,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB;AAAA,EACF,IAAI,QAAQ;AACZ,QAAM,CAAC,uBAAuB,wBAAwB,QAAI,uBAExD,IAAI;AAMN,MAAI,uBAAuB,0BAA0B,MAAM;AACzD,6BAAyB,IAAI;AAAA,EAC/B;AAGA,MACE,CAAC,uBACD,CAAC,6BACD,0BAA0B,OAC1B;AACA,6BAAyB,KAAK;AAAA,EAChC;AAEA,SACE,6BAAAC,QAAA;AAAA,IAAC,kBAAkB;AAAA,IAAlB;AAAA,MACC,OAAO;AAAA,QACL,WAAW,0BAA0B;AAAA,QACrC,iBACE,8BAA8B,yBAAyB;AAAA,MAC3D;AAAA;AAAA,IAEA,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,IACF;AAAA,IACA,6BAAAA,QAAA,cAAC,iCAAe,UAAwB,QAAS;AAAA,IACjD,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,IACF;AAAA,EACF;AAEJ;AAIA,SAAS,2BAA2B;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAUG;AACD,8BAAU,MAAM;AACd,QAAI,uBAAuB;AAC3B,QAAI,2BAA2B;AAC7B,aAAO,QAAQ,kBAAkB,CAAC,kCAAkC;AAClE,YAAI,sBAAsB;AACxB,mCAAyB,MAAM,6BAA6B;AAAA,QAC9D;AAAA,MACF,CAAC;AACD,aAAO,MAAM;AACX,+BAAuB;AAIvB;AAAA,UAAyB,CAAC,0BACxB,wBAAwB,QAAQ;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAKA,SAAS,0BAA0B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAUG;AACD,8BAAU,MAAM;AAEd,QAAI,2BAA2B;AAC7B,aAAO,MAAM;AACX,eAAO,UAAU;AAOjB,iCAAyB,MAAM,IAAI;AAAA,MACrC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AACT;", "names": ["import_client", "React"]}