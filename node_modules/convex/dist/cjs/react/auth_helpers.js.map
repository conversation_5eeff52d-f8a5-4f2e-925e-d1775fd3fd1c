{"version": 3, "sources": ["../../../src/react/auth_helpers.tsx"], "sourcesContent": ["import React from \"react\";\nimport { ReactNode } from \"react\";\nimport { useConvexAuth } from \"./ConvexAuthState.js\";\n\n/**\n * Renders children if the client is authenticated.\n *\n * @public\n */\nexport function Authenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || !isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client is using authentication but is not authenticated.\n *\n * @public\n */\nexport function Unauthenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client isn't using authentication or is in the process\n * of authenticating.\n *\n * @public\n */\nexport function AuthLoading({ children }: { children: ReactNode }) {\n  const { isLoading } = useConvexAuth();\n  if (!isLoading) {\n    return null;\n  }\n  return <>{children}</>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAElB,6BAA8B;AAOvB,SAAS,cAAc,EAAE,SAAS,GAA4B;AACnE,QAAM,EAAE,WAAW,gBAAgB,QAAI,sCAAc;AACrD,MAAI,aAAa,CAAC,iBAAiB;AACjC,WAAO;AAAA,EACT;AACA,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;AAOO,SAAS,gBAAgB,EAAE,SAAS,GAA4B;AACrE,QAAM,EAAE,WAAW,gBAAgB,QAAI,sCAAc;AACrD,MAAI,aAAa,iBAAiB;AAChC,WAAO;AAAA,EACT;AACA,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;AAQO,SAAS,YAAY,EAAE,SAAS,GAA4B;AACjE,QAAM,EAAE,UAAU,QAAI,sCAAc;AACpC,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;", "names": ["React"]}