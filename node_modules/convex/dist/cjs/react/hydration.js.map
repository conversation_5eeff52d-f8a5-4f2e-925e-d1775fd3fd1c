{"version": 3, "sources": ["../../../src/react/hydration.tsx"], "sourcesContent": ["import { useMemo } from \"react\";\nimport { useQuery } from \"../react/client.js\";\nimport { FunctionReference, makeFunctionReference } from \"../server/api.js\";\nimport { jsonToConvex } from \"../values/index.js\";\n\n/**\n * The preloaded query payload, which should be passed to a client component\n * and passed to {@link usePreloadedQuery}.\n *\n * @public\n */\nexport type Preloaded<Query extends FunctionReference<\"query\">> = {\n  __type: Query;\n  _name: string;\n  _argsJSON: string;\n  _valueJSON: string;\n};\n\n/**\n * Load a reactive query within a React component using a `Preloaded` payload\n * from a Server Component returned by {@link nextjs.preloadQuery}.\n *\n * This React hook contains internal state that will cause a rerender\n * whenever the query result changes.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param preloadedQuery - The `Preloaded` query payload from a Server Component.\n * @returns the result of the query. Initially returns the result fetched\n * by the Server Component. Subsequently returns the result fetched by the client.\n *\n * @public\n */\nexport function usePreloadedQuery<Query extends FunctionReference<\"query\">>(\n  preloadedQuery: Preloaded<Query>,\n): Query[\"_returnType\"] {\n  const args = useMemo(\n    () => jsonToConvex(preloadedQuery._argsJSON),\n    [preloadedQuery._argsJSON],\n  ) as Query[\"_args\"];\n  const preloadedResult = useMemo(\n    () => jsonToConvex(preloadedQuery._valueJSON),\n    [preloadedQuery._valueJSON],\n  );\n  const result = useQuery(\n    makeFunctionReference(preloadedQuery._name) as Query,\n    args,\n  );\n  return result === undefined ? preloadedResult : result;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAwB;AACxB,oBAAyB;AACzB,iBAAyD;AACzD,oBAA6B;AA8BtB,SAAS,kBACd,gBACsB;AACtB,QAAM,WAAO;AAAA,IACX,UAAM,4BAAa,eAAe,SAAS;AAAA,IAC3C,CAAC,eAAe,SAAS;AAAA,EAC3B;AACA,QAAM,sBAAkB;AAAA,IACtB,UAAM,4BAAa,eAAe,UAAU;AAAA,IAC5C,CAAC,eAAe,UAAU;AAAA,EAC5B;AACA,QAAM,aAAS;AAAA,QACb,kCAAsB,eAAe,KAAK;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,WAAW,SAAY,kBAAkB;AAClD;", "names": []}