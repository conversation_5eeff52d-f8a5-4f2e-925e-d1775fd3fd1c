{"version": 3, "sources": ["../../../src/react/index.ts"], "sourcesContent": ["/**\n * Tools to integrate Convex into React applications.\n *\n * This module contains:\n * 1. {@link ConvexReactClient}, a client for using Convex in React.\n * 2. {@link ConvexProvider}, a component that stores this client in React context.\n * 3. {@link Authenticated}, {@link Unauthenticated} and {@link AuthLoading} helper auth components.\n * 4. Hooks {@link useQuery}, {@link useMutation}, {@link useAction} and more for accessing this\n *    client from your React components.\n *\n * ## Usage\n *\n * ### Creating the client\n *\n * ```typescript\n * import { ConvexReactClient } from \"convex/react\";\n *\n * // typically loaded from an environment variable\n * const address = \"https://small-mouse-123.convex.cloud\"\n * const convex = new ConvexReactClient(address);\n * ```\n *\n * ### Storing the client in React Context\n *\n * ```typescript\n * import { ConvexProvider } from \"convex/react\";\n *\n * <ConvexProvider client={convex}>\n *   <App />\n * </ConvexProvider>\n * ```\n *\n * ### Using the auth helpers\n *\n * ```typescript\n * import { Authenticated, Unauthenticated, AuthLoading } from \"convex/react\";\n *\n * <Authenticated>\n *   Logged in\n * </Authenticated>\n * <Unauthenticated>\n *   Logged out\n * </Unauthenticated>\n * <AuthLoading>\n *   Still loading\n * </AuthLoading>\n * ```\n *\n * ### Using React hooks\n *\n * ```typescript\n * import { useQuery, useMutation } from \"convex/react\";\n * import { api } from \"../convex/_generated/api\";\n *\n * function App() {\n *   const counter = useQuery(api.getCounter.default);\n *   const increment = useMutation(api.incrementCounter.default);\n *   // Your component here!\n * }\n * ```\n * @module\n */\nexport * from \"./use_paginated_query.js\";\nexport { useQueries, type RequestForQueries } from \"./use_queries.js\";\nexport type { AuthTokenFetcher } from \"../browser/sync/client.js\";\nexport * from \"./auth_helpers.js\";\nexport * from \"./ConvexAuthState.js\";\nexport * from \"./hydration.js\";\n/* @internal */\nexport { useSubscription } from \"./use_subscription.js\";\nexport {\n  type ReactMutation,\n  type ReactAction,\n  type Watch,\n  type WatchQueryOptions,\n  type MutationOptions,\n  type ConvexReactClientOptions,\n  type OptionalRestArgsOrSkip,\n  ConvexReactClient,\n  useConvex,\n  ConvexProvider,\n  useQuery,\n  useMutation,\n  useAction,\n} from \"./client.js\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8DA,0BAAc,qCA9Dd;AA+DA,yBAAmD;AAEnD,0BAAc,8BAjEd;AAkEA,0BAAc,iCAlEd;AAmEA,0BAAc,2BAnEd;AAqEA,8BAAgC;AAChC,oBAcO;", "names": []}